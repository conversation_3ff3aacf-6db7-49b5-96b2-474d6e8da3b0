# 需求文档

## 介绍

当前分支进行了重大重构以支持多个企业微信账号。所有接口和定时任务都已修改为通过`companyNo`字段支持多企业功能。所有Dubbo接口必须支持查询指定企业的信息，默认查询好买财富(companyNo=1)以保持与现有生产接口调用的兼容性。本次迭代中消息发送功能不支持多企业。重构已完成，所有代码已提交到当前分支。任务是进行全面的代码审查，通过代码审查方法识别任何遗漏的更改、错误的修改或bug，因为系统目前缺乏自动化测试。

## 需求

### 需求 1

**用户故事:** 作为系统维护人员，我希望验证所有Dubbo接口都已正确修改以支持多企业功能，以便系统能够正确处理多个企业微信账号。

#### 验收标准

1. 当审查Dubbo接口实现时，系统应在所有相关接口中包含companyNo参数支持
2. 当调用Dubbo接口时未提供companyNo参数，系统应默认使用companyNo=1（好买财富）
3. 当使用特定companyNo调用Dubbo接口时，系统应仅返回该企业的数据
4. 如果任何Dubbo接口缺乏多企业支持，系统应标记为需要审查

### 需求 2

**用户故事:** 作为系统维护人员，我希望验证所有定时任务都已更新以支持多企业操作，以便后台进程在所有企业账号中正确工作。

#### 验收标准

1. 当审查定时任务实现时，系统应为所有相关企业处理数据或接受companyNo参数
2. 当定时任务处理数据时，系统应保持不同企业之间的数据隔离
3. 如果任何定时任务缺乏多企业支持，系统应标记为需要审查
4. 当定时任务查询数据时，系统应使用适当的companyNo过滤

### 需求 3

**用户故事:** 作为系统维护人员，我希望验证数据库操作已正确更新companyNo过滤，以便数据查询返回正确的企业特定结果。

#### 验收标准

1. 当审查数据库mapper方法时，系统应在适当的WHERE子句中包含companyNo
2. 当审查SQL查询时，系统应通过companyNo过滤以确保数据隔离
3. 如果任何数据库操作缺乏companyNo过滤，系统应标记为需要审查
4. 当执行默认查询时，系统应使用companyNo=1作为默认值

### 需求 4

**用户故事:** 作为系统维护人员，我希望验证服务层实现正确处理多企业逻辑，以便业务操作在不同企业账号中正确工作。

#### 验收标准

1. 当审查服务实现时，系统应通过调用链传递companyNo参数
2. 当服务方法处理数据时，系统应维护企业特定的上下文
3. 如果任何服务方法缺乏多企业参数处理，系统应标记为需要审查
4. 当服务与外部系统交互时，系统应使用正确的企业特定配置

### 需求 5

**用户故事:** 作为系统维护人员，我希望验证控制器端点已更新以处理多企业请求，以便API消费者可以指定访问哪个企业的数据。

#### 验收标准

1. 当审查控制器方法时，系统应在请求中接受companyNo参数
2. 当调用控制器方法时未提供companyNo，系统应默认使用companyNo=1
3. 如果任何控制器缺乏多企业参数支持，系统应标记为需要审查
4. 当控制器验证输入时，系统应确保companyNo是有效的

### 需求 6

**用户故事:** 作为系统维护人员，我希望识别多企业重构过程中引入的任何不一致性或bug，以便系统保持可靠性和正确性。

#### 验收标准

1. 当审查代码更改时，系统应识别多企业处理中的任何逻辑不一致性
2. 当审查参数传递时，系统应确保companyNo在所有层中正确传播
3. 如果任何代码显示潜在的bug或错误逻辑，系统应记录以供修复
4. 当审查配置处理时，系统应确保企业特定配置正确加载

### 需求 7

**用户故事:** 作为系统维护人员，我希望验证消息发送功能保持不变且不受多企业更改影响，以便现有消息功能继续按预期工作。

#### 验收标准

1. 当审查消息发送代码时，系统应保持原始的单企业行为
2. 当调用消息发送接口时，系统应在不需要companyNo参数的情况下工作
3. 如果消息发送代码被无意中修改为多企业，系统应标记为需要审查
4. 当消息发送进程执行时，系统应保持向后兼容性