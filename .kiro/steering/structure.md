# Project Structure

## Module Organization

The project follows a layered architecture with clear separation of concerns across 4 main modules:

```
crm-wechat/                     # Parent project
├── crm-wechat-client/          # API interfaces and DTOs
├── crm-wechat-dao/             # Data access layer
├── crm-wechat-service/         # Business logic layer
└── crm-wechat-remote/          # Application entry point
```

## Module Responsibilities

### crm-wechat-client
- **Purpose**: Public API definitions and shared models
- **Contains**: Facades, request/response DTOs, enums, base classes
- **Key Packages**:
  - `base/`: Common request/response base classes
  - `domain/request/`: Request DTOs organized by business domain
  - `domain/response/`: Response DTOs organized by business domain
  - `enums/`: Business enums and constants
  - `facade/`: Service interface definitions
  - `producer/`: Dubbo service interfaces

### crm-wechat-dao
- **Purpose**: Data persistence layer
- **Contains**: MyBatis mappers, POJOs, database configurations
- **Key Packages**:
  - `mapper/`: MyBatis mapper interfaces
  - `mapper/customize/`: Custom SQL mapper interfaces
  - `po/`: Persistent objects (database entities)
  - `bo/`: Business objects for complex queries
  - `vo/`: Value objects for data transfer

### crm-wechat-service
- **Purpose**: Core business logic and service implementations
- **Contains**: Service implementations, controllers, configurations
- **Key Packages**:
  - `facade/`: Dubbo service implementations
  - `service/`: Business service layer
  - `controller/`: REST API controllers
  - `business/`: Complex business logic handlers
  - `job/`: Scheduled tasks and batch processing
  - `repository/`: Data access abstraction layer
  - `config/`: Spring configuration classes
  - `commom/`: Utilities, constants, and common components

### crm-wechat-remote
- **Purpose**: Application bootstrap and deployment
- **Contains**: Main application class, configuration files
- **Key Files**:
  - `CrmWechatApplication.java`: Spring Boot main class
  - Configuration files: `bootstrap.properties`, `dubbo.xml`

## Package Naming Conventions

### Business Domain Organization
Services are organized by WeChat business domains:
- `wechatauth/`: Authentication services
- `wechatcustinfo/`: Customer information management
- `wechatgroup/`: Group management
- `wechatjssdk/`: JavaScript SDK integration
- `wechatmaterial/`: Material/media management

### Layer-Specific Patterns
- **Controllers**: `*Controller.java` - REST endpoints
- **Services**: `*Service.java` - Business logic
- **Repositories**: `*Repository.java` - Data access
- **Facades**: `*FacadeImpl.java` - Dubbo service implementations
- **Mappers**: `*Mapper.java` - MyBatis interfaces
- **POJOs**: `*PO.java` - Database entities
- **DTOs**: `*DTO.java` - Data transfer objects
- **VOs**: `*VO.java` - Value objects

## Configuration Structure

### Resource Organization
- `src/main/resources/`: Main configuration files
- `src/main/resources/com/howbuy/crm/wechat/dao/mapper/`: MyBatis XML mappings
- `src/test/resources/`: Test-specific configurations

### Key Configuration Files
- `bootstrap.properties`: Bootstrap configuration
- `dubbo.xml`: Dubbo service configuration
- `log4j2-*.xml`: Logging configuration
- `ehcache-cluster.xml`: Cache configuration

## Database Schema Organization
Tables follow naming convention: `cm_wechat_*`
- Core entities: `cm_wechat_cust_info`, `cm_wechat_group`, `cm_wechat_emp`
- Relationship tables: `cm_wechat_cust_relation`, `cm_wechat_group_user`
- Message tables: `message_send_info`, `message_accept_info`
- History tables: `*_change_his` for audit trails