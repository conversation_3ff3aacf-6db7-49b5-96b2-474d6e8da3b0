# Technology Stack

## Core Technologies
- **Java**: JDK 1.8
- **Spring Boot**: 2.3.7.RELEASE
- **Spring Cloud**: Hoxton.SR9
- **Spring Cloud Alibaba**: 2.2.6.RELEASE
- **Apache Dubbo**: 3.2.12 (RPC framework)
- **MyBatis**: 2.2.2 (ORM)
- **Maven**: Multi-module project structure

## Key Dependencies
- **Database**: Oracle (ojdbc8), MySQL (mysql-connector-java)
- **Connection Pool**: Druid 1.2.8
- **Cache**: Redis (Jedis 2.9.3), Ehcache
- **Logging**: Log4j2 2.15.0, SLF4J
- **JSON**: FastJSON 2.0.32
- **Service Discovery**: Nacos
- **Message Queue**: ActiveMQ, RocketMQ
- **Pagination**: PageHelper 5.3.0

## Build System
Maven multi-module project with the following structure:
- Parent POM manages all dependency versions
- Each module has its own POM inheriting from parent
- Uses Maven dependency plugin for packaging

## Common Build Commands

### Development
```bash
# Build entire project
mvn clean compile

# Package all modules
mvn clean package

# Skip tests during build
mvn clean package -DskipTests

# Install to local repository
mvn clean install
```

### Deployment
```bash
# Start application (production)
./start.sh

# Start with debug mode
./start.sh debug

# Start with JMX monitoring
./start.sh jmx
```

### Testing
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=ClassName

# Run tests with coverage
mvn clean test jacoco:report
```

## Configuration Management
- **CCMS**: Centralized configuration management
- **Nacos**: Service discovery and configuration center
- **Properties**: Environment-specific configuration files
- **Dubbo XML**: Service provider/consumer configuration

## Transaction Management
- Uses Spring `@Transactional` annotations
- Default propagation: `SUPPORTS` for read operations
- Required propagation: `REQUIRED` for write operations
- Rollback on all exceptions: `rollbackFor = Exception.class`