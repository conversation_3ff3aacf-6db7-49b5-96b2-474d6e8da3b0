---
inclusion: always
---
1.总是用中文回答

# Global Development Guidelines

## Code Style & Conventions

### Java Coding Standards
- **Package Structure**: Follow reverse domain naming: `com.howbuy.crm.wechat.*`
- **Class Naming**: Use PascalCase with descriptive suffixes (`*Service`, `*Controller`, `*Repository`)
- **Method Naming**: Use camelCase with verb-noun patterns (`getUserInfo`, `updateCustomerRelation`)
- **Constants**: Use UPPER_SNAKE_CASE in dedicated constant classes
- **Variable Naming**: Use camelCase, avoid abbreviations except for well-known terms (id, url, dto)

### Documentation Requirements
- **Class-level**: Document purpose, key responsibilities, and usage patterns
- **Method-level**: Document parameters, return values, and business logic for complex methods
- **API Endpoints**: Include request/response examples and error scenarios
- **Database Changes**: Document schema changes and migration scripts

### Error Handling Patterns
- Use `BusinessException` for business logic violations
- Wrap external service calls with appropriate exception handling
- Log errors with sufficient context (user ID, company, operation)
- Return standardized error responses using `Response<T>` wrapper

## Architecture Patterns

### Service Layer Design
- **Single Responsibility**: Each service handles one business domain
- **Dependency Injection**: Use `@Autowired` with constructor injection preferred
- **Transaction Management**: Apply `@Transactional` at service method level
- **Async Processing**: Use `@Async` for non-blocking operations (message sending, data sync)

### Data Access Patterns
- **Repository Pattern**: Abstract database operations through repository interfaces
- **Custom Queries**: Place complex SQL in `customize` mapper packages
- **Batch Operations**: Use batch processing for bulk data operations
- **Pagination**: Always implement pagination for list queries using PageHelper

### Integration Patterns
- **Dubbo Services**: Implement interfaces in `crm-wechat-client`, provide in `crm-wechat-service`
- **External APIs**: Use `RestTemplate` with proper timeout and retry configuration
- **Message Queues**: Implement idempotent message processing
- **Cache Strategy**: Use Redis for session data, Ehcache for configuration data

## WeChat-Specific Rules

### Multi-Company Support
- Always validate `companyNo` parameter in requests
- Use company-specific configuration for WeChat API calls
- Isolate data by company in database queries
- Handle company-specific business rules in service layer

### API Integration
- **Rate Limiting**: Respect WeChat API rate limits (implement backoff strategies)
- **Token Management**: Cache and refresh access tokens automatically
- **Callback Handling**: Validate signatures and handle duplicate callbacks
- **Error Recovery**: Implement retry logic for transient failures

### Data Synchronization
- **Incremental Sync**: Use timestamp-based incremental updates where possible
- **Conflict Resolution**: Implement last-write-wins for data conflicts
- **Audit Trail**: Maintain change history in `*_change_his` tables
- **Data Validation**: Validate WeChat data before persisting

## Security Guidelines

### Authentication & Authorization
- Validate WeChat signatures for all callback requests
- Implement proper session management for web endpoints
- Use company-specific access controls
- Log all authentication attempts and failures

### Data Protection
- Encrypt sensitive customer data at rest
- Mask PII in logs and error messages
- Implement data retention policies
- Follow GDPR/privacy requirements for customer data

## Performance Guidelines

### Database Optimization
- Use appropriate indexes for query patterns
- Implement connection pooling with Druid
- Monitor slow queries and optimize regularly
- Use read replicas for reporting queries

### Caching Strategy
- Cache frequently accessed configuration data
- Implement cache invalidation for data updates
- Use distributed caching for multi-instance deployments
- Monitor cache hit rates and adjust TTL accordingly

### Monitoring & Observability
- Log business operations with correlation IDs
- Implement health checks for external dependencies
- Monitor API response times and error rates
- Use structured logging for better searchability

## Testing Standards

### Unit Testing
- Achieve minimum 70% code coverage
- Mock external dependencies (WeChat APIs, databases)
- Test both success and failure scenarios
- Use meaningful test method names describing scenarios

### Integration Testing
- Test complete business workflows
- Validate database transactions and rollbacks
- Test WeChat API integration with mock servers
- Verify message queue processing end-to-end
