# SyncCustHboneRelationJob 定时任务性能优化方案

## 1. 任务概述

`SyncCustHboneRelationJob` 是一个用于同步企业微信客户关系的定时任务，主要功能是：
- 更新所有高端客户微信信息
- 同步企业微信客户与投顾的关联关系
- 处理管理员后台分配的客户划转逻辑

## 2. 当前执行流程分析

```
定时任务启动
    ↓
初始化待处理投顾列表 (从CRM账户中心获取)
    ↓
使用线程池并发处理每个投顾
    ↓
对每个投顾：
    1. 获取其添加的所有外部客户ID列表 (企微API调用)
    2. 如果客户数>500，分批处理 (每批500个)
    3. 对每个客户：
       - 调用企微API获取详细信息 (getExternalUser)
       - 调用账户中心获取一账通号 (getHboneNoByAccCenter)
       - 更新客户信息表 (insertCmWechatCustInfo)
       - 更新客户关系表 (insertCmWechatCustRelationForTask)
    ↓
处理管理员划转客户逻辑
    ↓
更新任务状态为完成
```

## 3. 性能瓶颈分析

### 3.1 API调用瓶颈 (最严重)
- **企微API调用频次过高**: 对每个客户都调用 `getExternalUser` 获取详细信息
- **账户中心API调用频次过高**: 对每个客户都调用 `getHboneNoByAccCenter` 获取一账通号
- **API调用串行化**: 每个客户的API调用都是串行执行

**影响**: 假设一个投顾有10,000个客户，需要调用20,000次API (10,000次企微API + 10,000次账户中心API)

### 3.2 数据库操作瓶颈
- **单条数据库插入/更新**: 每个客户的信息更新和关系更新都是单独的数据库操作
- **频繁的数据库连接**: 没有使用批量操作

### 3.3 内存使用问题
- **大量数据驻留内存**: 所有客户信息都加载到内存中处理
- **ConcurrentHashMap无限增长**: `externalIdUserIdsMap` 可能占用大量内存

### 3.4 线程池配置问题
- **线程池嵌套**: `updateAllCustInfoExecutor` 中又使用了 `dealSingleExecutor`
- **任务超时时间过长**: 单个任务超时时间设置为60分钟，可能导致线程池阻塞

## 4. 优化方案

### 4.1 API调用优化 (优先级：最高)

#### 4.1.1 批量API调用
```java
// 当前实现 (每个客户单独调用)
for (String externalUserId : externalUserIds) {
    ExternalUserInfoDTO externalUser = getExternalUser(externalUserId, companyNoEnum, false);
    String hboneNo = getHboneNoByAccCenter(externalUser.getUnionid());
}

// 优化后实现 (批量调用)
List<ExternalUserInfoDTO> externalUsers = batchGetExternalUsers(externalUserIds, companyNoEnum);
Map<String, String> unionidHboneMap = batchGetHboneNoByUnionids(unionids);
```

#### 4.1.2 增加缓存机制
```java
@Cacheable(value = "externalUserCache", key = "#externalUserId + '_' + #companyNoEnum.code")
public ExternalUserInfoDTO getExternalUser(String externalUserId, CompanyNoEnum companyNoEnum) {
    // 原有逻辑
}

@Cacheable(value = "hboneNoCache", key = "#unionid", expire = 3600)
public String getHboneNoByAccCenter(String unionid) {
    // 原有逻辑
}
```

#### 4.1.3 API调用并行化
```java
// 使用CompletableFuture并行调用
List<CompletableFuture<ExternalUserInfoDTO>> futures = externalUserIds.stream()
    .map(id -> CompletableFuture.supplyAsync(() -> getExternalUser(id, companyNoEnum), apiExecutor))
    .collect(Collectors.toList());

List<ExternalUserInfoDTO> results = futures.stream()
    .map(CompletableFuture::join)
    .collect(Collectors.toList());
```

### 4.2 数据库操作优化 (优先级：高)

#### 4.2.1 批量数据库操作
```java
// 当前实现 (单条插入)
cmWechatCustInfoRepository.insertCmWechatCustInfo(custInfo, companyNoEnum);

// 优化后实现 (批量插入)
List<CmWechatCustInfoPO> custInfoList = new ArrayList<>();
// 收集所有要插入的数据
custInfoList.add(custInfo);
// 批量插入 (每批1000条)
cmWechatCustInfoRepository.batchInsertCmWechatCustInfo(custInfoList, companyNoEnum);
```

#### 4.2.2 数据库连接池优化
```yaml
# 增加数据库连接池配置
spring:
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 20
      connection-timeout: 60000
      idle-timeout: 600000
```

### 4.3 内存优化 (优先级：中)

#### 4.3.1 流式处理
```java
// 当前实现 (全量加载)
List<String> allExternalUserIds = getExternalUserIdList(wechatconscode, companyNoEnum);
processBatch(allExternalUserIds, wechatconscode, externalIdUserIdsMap);

// 优化后实现 (流式处理)
try (Stream<String> externalUserIdStream = getExternalUserIdStream(wechatconscode, companyNoEnum)) {
    externalUserIdStream
        .collect(Collectors.groupingBy(id -> id.hashCode() % batchSize))
        .values()
        .parallelStream()
        .forEach(batch -> processBatch(batch, wechatconscode, externalIdUserIdsMap));
}
```

#### 4.3.2 分页处理
```java
public void processLargeDataSet(String wechatconscode, CompanyNoEnum companyNoEnum) {
    int pageSize = 1000;
    int pageNum = 0;
    List<String> batch;
    
    do {
        batch = getExternalUserIdList(wechatconscode, companyNoEnum, pageNum * pageSize, pageSize);
        if (!batch.isEmpty()) {
            processBatch(batch, wechatconscode, externalIdUserIdsMap);
        }
        pageNum++;
    } while (batch.size() == pageSize);
}
```

### 4.4 线程池优化 (优先级：中)

#### 4.4.1 线程池配置优化
```java
@Bean("optimizedTaskExecutor")
public ThreadPoolTaskExecutor optimizedTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(20);           // 核心线程数
    executor.setMaxPoolSize(50);            // 最大线程数
    executor.setQueueCapacity(1000);        // 队列容量
    executor.setKeepAliveSeconds(60);       // 线程存活时间
    executor.setThreadNamePrefix("sync-cust-");
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    return executor;
}
```

#### 4.4.2 避免线程池嵌套
```java
// 当前实现 (嵌套线程池)
updateAllCustInfoExecutor.submit(() -> {
    dealSingleExecutor.submit(() -> {
        // 处理逻辑
    });
});

// 优化后实现 (单一线程池 + 合理任务拆分)
List<Future<ProcessResult>> futures = new ArrayList<>();
for (ProcessTask task : tasks) {
    futures.add(optimizedTaskExecutor.submit(task));
}
```

### 4.5 业务逻辑优化 (优先级：中)

#### 4.5.1 增量更新机制
```java
// 增加增量更新逻辑
public void incrementalUpdate(CompanyNoEnum companyNoEnum) {
    String lastUpdateTime = getLastUpdateTime(companyNoEnum);
    List<String> changedExternalUserIds = getChangedExternalUserIds(lastUpdateTime, companyNoEnum);
    
    // 只处理变更的数据
    processChangedUsers(changedExternalUserIds, companyNoEnum);
}
```

#### 4.5.2 智能跳过逻辑
```java
// 跳过已经是最新数据的客户
if (isDataUpToDate(externalUserId, companyNoEnum)) {
    log.debug("跳过已经是最新的客户数据: {}", externalUserId);
    continue;
}
```

## 5. 实施计划

### 第一阶段 (立即实施 - 预期提升50%性能)
1. **API调用缓存** - 为 `getExternalUser` 和 `getHboneNoByAccCenter` 添加缓存
2. **批量数据库操作** - 实现批量插入/更新
3. **线程池配置优化** - 调整线程池参数

### 第二阶段 (2周内 - 预期再提升30%性能)
1. **批量API调用** - 改造为批量API调用
2. **API调用并行化** - 使用CompletableFuture并行调用
3. **内存优化** - 实现流式处理

### 第三阶段 (1个月内 - 预期再提升20%性能)
1. **增量更新机制** - 只处理变更数据
2. **智能跳过逻辑** - 避免重复处理
3. **监控和告警** - 添加性能监控

## 6. 风险评估

### 6.1 技术风险
- **API限频风险**: 批量调用可能触发企微API限频
- **内存溢出风险**: 大批量数据处理可能导致OOM
- **数据一致性风险**: 并行处理可能导致数据不一致

### 6.2 缓解措施
```java
// API限频保护
@RateLimiter(value = "wechatApi", fallback = "fallbackMethod")
public ExternalUserInfoDTO getExternalUser(String externalUserId, CompanyNoEnum companyNoEnum) {
    // 实现逻辑
}

// 内存监控
@EventListener
public void handleLowMemory(MemoryWarningEvent event) {
    log.warn("内存使用率过高，暂停任务执行");
    pauseTask();
}
```

## 7. 预期效果

### 7.1 性能提升预期
- **整体执行时间**: 从目前的几小时缩短到30分钟以内
- **API调用次数**: 减少60-80%
- **数据库操作次数**: 减少90%以上
- **内存使用**: 减少50%

### 7.2 投顾客户数量 vs 处理时间预估

| 客户数量 | 当前耗时 | 优化后耗时 | 性能提升 |
|---------|---------|-----------|----------|
| 1,000   | 10分钟   | 2分钟     | 80%      |
| 10,000  | 2小时    | 15分钟    | 87.5%    |
| 50,000  | 10小时   | 1小时     | 90%      |

## 8. 监控指标

### 8.1 关键指标
- 任务总执行时间
- API调用成功率和响应时间
- 数据库操作耗时
- 内存使用峰值
- 线程池队列长度

### 8.2 告警规则
```yaml
alerts:
  - name: "同步任务执行时间过长"
    condition: "execution_time > 3600s"
    action: "发送告警邮件"
  
  - name: "API调用失败率过高"
    condition: "api_error_rate > 5%"
    action: "发送告警短信"
```

## 9. 总结

通过以上优化方案的实施，预期可以将 `SyncCustHboneRelationJob` 的执行效率提升80-90%，特别是对于拥有大量客户的投顾，性能提升会更加明显。优化的核心在于减少API调用次数、实现批量操作、增加缓存机制和优化线程池配置。

建议按照实施计划分阶段推进，每个阶段完成后进行性能测试和验证，确保优化效果符合预期。