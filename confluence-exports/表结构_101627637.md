---
title: "海外交易-资金二期设计"
pageId: "101627637"
spaceKey: "lsm"
spaceId: "38600706"
author: "李少阳"
createdDate: "2025-07-25T09:56:29.000+08:00"
lastModified: "2025-07-25T09:56:29.000+08:00"
version: 207
confluenceUrl: "http://dms.intelnal.howbuy.com//pages/viewpage.action?pageId=101627637"
---
# 表结构

## 海外支付订单

```sql

CREATE TABLE `hw_payment_order`
(
    `id`                 BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `pmt_deal_no`        BIGINT(20) NOT NULL COMMENT '支付订单号',
    `deal_no`            BIGINT(20) NULL DEFAULT NULL COMMENT '订单号',
    `payment_type_list`  VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式列表 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐',
    `hk_cust_no`         VARCHAR(20) NOT NULL COMMENT '香港客户号',
    `cp_acct_no`         VARCHAR(32) NULL DEFAULT NULL COMMENT '资金账号',
    `fund_tx_acct_no`    VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号',
    `fund_code`          VARCHAR(6)  NOT NULL COMMENT '基金代码',
    `currency`           VARCHAR(3) NULL DEFAULT NULL COMMENT '币种',
    `app_dtm`            DATETIME(6) NULL DEFAULT NULL COMMENT '申请时间',
    `pmt_amt`            DECIMAL(19, 2) NULL DEFAULT NULL COMMENT '支付金额',
    `product_pay_end_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '产品打款截止日期',
    `product_pay_end_dm` VARCHAR(6) NULL DEFAULT NULL COMMENT '产品打款截止时间',
    `tx_pmt_flag`        VARCHAR(2) NULL DEFAULT NULL COMMENT '交易支付标记 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；',
    `pmt_comp_flag`      CHAR(1) NULL DEFAULT NULL COMMENT '支付对账标记0-无需对账；1-未对账；2-对账完成；3-对账不平;',
    `pmt_check_dt`       VARCHAR(8) NULL DEFAULT NULL COMMENT '支付对账日期',
    `out_pmt_deal_no`    VARCHAR(50) NULL DEFAULT NULL COMMENT '外部支付订单号',
    `pmt_org_code`       VARCHAR(30) NULL DEFAULT NULL COMMENT '支付机构代码',
    `ret_code`           VARCHAR(30) NULL DEFAULT NULL COMMENT '支付返回码',
    `ret_desc`           VARCHAR(1024) NULL DEFAULT NULL COMMENT '支付返回描述',
    `pmt_complete_dtm`   DATETIME(6) NULL DEFAULT NULL COMMENT '支付完成日期时间',
    `outlet_code`        VARCHAR(9) NULL DEFAULT NULL COMMENT '网点号',
    `trade_channel`      VARCHAR(2)  NOT NULL COMMENT '交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；',
    `memo`               VARCHAR(1024) NULL DEFAULT NULL COMMENT '备注',
    `order_type`         VARCHAR(2)  NOT NULL COMMENT '订单类型 1-交易 2-edda入金',
    `rec_stat`           CHAR(1)     NOT NULL DEFAULT '1' COMMENT '记录状态 0-无效 1-正常',
    `version`            INT(11) NULL DEFAULT '0' COMMENT '版本号',
    `create_timestamp`   DATETIME(6) NOT NULL COMMENT '创建时间戳',
    `update_timestamp`   DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_pmt_deal_no` (`pmt_deal_no`, `version`),
    INDEX                `idx_pmtorder_dealno` (`deal_no`),
    INDEX                `idx_pmtorder_pmt_check_dt` (`pmt_check_dt`),
    INDEX                `idx_pmtorder_updatedtm` (`update_timestamp`)
) COMMENT='海外支付订单'
;

```

## 海外支付对账结果

```sql
CREATE TABLE `hw_payment_check_result` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
	`pmt_deal_no` BIGINT(20) NOT NULL COMMENT '支付订单号',
	`deal_no` BIGINT(20) NULL DEFAULT NULL COMMENT '订单号',
	`hk_cust_no` VARCHAR(20) NOT NULL COMMENT '香港客户号',
	`fund_code` VARCHAR(6) NOT NULL COMMENT '基金代码',
	`currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '币种',
	`pmt_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '支付金额',
	`pmt_check_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '支付对账日期',
	`out_pmt_deal_no` VARCHAR(50) NULL DEFAULT NULL COMMENT '外部支付订单号',
	`out_pmt_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '外部支付金额',
	`out_currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '外部币种',
	`out_pmt_flag` VARCHAR(2) NULL DEFAULT NULL COMMENT '外部支付标识 1-待支付 2-支付成功 3-支付失败 4-支付中',
	`tx_pmt_flag` VARCHAR(2) NULL DEFAULT NULL COMMENT '交易支付标识 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中',
	`pmt_comp_flag` CHAR(1) NULL DEFAULT NULL COMMENT '支付对账标记0-无需对账；1-未对账；2-对账完成；3-对账不平;',
	`memo` VARCHAR(1024) NULL DEFAULT NULL COMMENT '备注',
	`rec_stat` CHAR(1) NOT NULL DEFAULT '1' COMMENT '记录状态 0-无效 1-正常',
	`version` INT(11) NULL DEFAULT 0 COMMENT '版本号',
	`create_timestamp` DATETIME(6) NOT NULL COMMENT '创建时间戳',
	`update_timestamp` DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
	PRIMARY KEY (`id`),
	UNIQUE INDEX `uk_pay_check_result_no` (`pmt_deal_no`, `version`),
	INDEX `idx_pay_check_result_dealno` (`deal_no`),
	INDEX `idx_pay_check_result_pmt_check_dt` (`pmt_check_dt`)
)
COMMENT='海外支付对账结果'
;
```

## 海外资金退款表

```sql
CREATE TABLE `hw_capital_refund` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `deal_no` BIGINT(20) NOT NULL COMMENT '订单号',
    `relational_deal_no` BIGINT(20) NULL DEFAULT NULL COMMENT '关联订单号(订单表的订单号)',
    `hk_cust_no` VARCHAR(20) NOT NULL COMMENT '香港客户号',
    `fund_tx_acct_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号',
    `fund_code` VARCHAR(6) NOT NULL COMMENT '基金代码',
    `fund_abbr` VARCHAR(200) NULL DEFAULT NULL COMMENT '基金简称',
    `fund_currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '基金币种',
    `fund_man_code` VARCHAR(18) NULL DEFAULT NULL COMMENT '管理人代码',
    `middle_busi_code` VARCHAR(5) NULL DEFAULT NULL COMMENT '中台业务码',
    `payment_type` VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式 1：电汇；2：支票；3：海外储蓄罐',
    `refund_direction` CHAR(1) NULL DEFAULT NULL COMMENT '退款方向 2-现金余额',
    `refund_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '退款金额',
    `refund_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '退款日期 YYYYMMDD',
	`handle_status` VARCHAR(1) NULL DEFAULT NULL COMMENT '处理状态 0-未处理 1-处理成功',
    `rec_stat` CHAR(1) NOT NULL DEFAULT '1' COMMENT '记录状态 0-无效 1-正常',
	`version` INT(11) NULL DEFAULT 0 COMMENT '版本号',
    `create_timestamp` DATETIME(6) NOT NULL COMMENT '创建时间戳',
    `update_timestamp` DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_capital_refund_deal_no` (`deal_no`, `version`),
    INDEX `idx_capital_refund_refund_dt` (`refund_dt`)
)
COMMENT='海外资金退款表';
```

## IO资金退款导出表

```sql
CREATE TABLE `io_capital_refund_export` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
	`export_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '导出日期',
	`file_id` BIGINT(20) NULL DEFAULT NULL COMMENT '文件id',
    `deal_no` BIGINT(20) NOT NULL COMMENT '订单号',
    `relational_deal_no` BIGINT(20) NULL DEFAULT NULL COMMENT '关联订单号(订单表的订单号)',
    `hk_cust_no` VARCHAR(20) NOT NULL COMMENT '香港客户号',
    `fund_tx_acct_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号',
    `fund_code` VARCHAR(6) NOT NULL COMMENT '基金代码',
    `fund_abbr` VARCHAR(200) NULL DEFAULT NULL COMMENT '基金简称',
    `fund_currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '基金币种',
    `fund_man_code` VARCHAR(18) NULL DEFAULT NULL COMMENT '管理人代码',
    `busi_code` VARCHAR(5) NULL DEFAULT NULL COMMENT '业务码',
    `payment_type` VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式 1：电汇；2：支票；3：海外储蓄罐',
    `refund_direction` CHAR(1) NULL DEFAULT NULL COMMENT '退款方向 2-现金余额',
    `refund_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '退款金额',
    `refund_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '退款日期 YYYYMMDD',
    `create_timestamp` DATETIME(6) NOT NULL COMMENT '创建时间戳',
    `update_timestamp` DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
    PRIMARY KEY (`id`),
    INDEX `idx_io_refund_deal_no` (`deal_no`),
    INDEX `idx_io_refund_file_id` (`file_id`),
    INDEX `idx_io_refund_export_dt` (`export_dt`)
)
COMMENT='io资金退款导出表';
```

## 海外储蓄罐导入申请表

```sql
ALTER TABLE `hw_piggy_trade_app_import`
	ADD UNIQUE INDEX `uk_import_app_id` (`import_app_id`, `version`),
	ADD COLUMN `fund_tx_acct_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号' AFTER `hk_cust_no`,
	CHANGE COLUMN `buy_amt` `buy_amt` DECIMAL(16,2) NULL DEFAULT NULL COMMENT '购买金额' AFTER `pre_submit_ta_dt`,
	ADD COLUMN `discount_rate` DECIMAL(8,4) NULL DEFAULT NULL COMMENT '折扣率' AFTER `fee`,
	CHANGE COLUMN `payment_type` `payment_type` VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式  1-电汇；2-支票；3-海外储蓄罐' AFTER `discount_rate`,
	CHANGE COLUMN `app_amt` `app_amt` DECIMAL(16,2) NULL DEFAULT NULL COMMENT '申请金额' AFTER `redeem_type`,
	CHANGE COLUMN `redeem_direction` `redeem_direction` VARCHAR(4) NULL DEFAULT NULL COMMENT '回款方向 1-回银行卡；2-留账好买香港账户；3-回海外储蓄罐；4-基金转投；' AFTER `app_vol`,
	CHANGE COLUMN `is_generated` `is_generated` VARCHAR(1) NULL DEFAULT NULL COMMENT '是否生成订单  0-未生成 1-已生成 2-生成失败' AFTER `import_dt`,
	ADD COLUMN `deal_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '订单号' AFTER `is_generated`,
    ADD COLUMN `piggy_app_source` VARCHAR(20) NULL DEFAULT NULL COMMENT '储蓄罐申请来源 0-excel 1-可用余额、2-客户控管表、3-退款控管表、4-自动赎回' AFTER `deal_dtl_no`;

ALTER TABLE `hw_piggy_trade_app_import`
	DROP COLUMN `pay_end_dt`,
	DROP COLUMN `actual_pay_dt`,
	DROP COLUMN `actual_pay_amt`,
	DROP COLUMN `deal_dtl_no`;
```

## 海外订单明细表

```sql
ALTER TABLE `hw_deal_order_dtl`
ADD COLUMN `product_pay_end_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '产品打款截止日期' AFTER `pay_end_tm`,
ADD COLUMN `product_pay_end_dm` VARCHAR(6) NULL DEFAULT NULL COMMENT '产品打款截止时间' AFTER `product_pay_end_dt`;

```

## 海外支付订单

```sql

CREATE TABLE `hw_payment_order`
(
    `id`                 BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `pmt_deal_no`        BIGINT(20) NOT NULL COMMENT '支付订单号',
    `deal_no`            BIGINT(20) NULL DEFAULT NULL COMMENT '订单号',
    `payment_type_list`  VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式列表 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐',
    `hk_cust_no`         VARCHAR(20) NOT NULL COMMENT '香港客户号',
    `cp_acct_no`         VARCHAR(32) NULL DEFAULT NULL COMMENT '资金账号',
    `fund_tx_acct_no`    VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号',
    `fund_code`          VARCHAR(6)  NOT NULL COMMENT '基金代码',
    `currency`           VARCHAR(3) NULL DEFAULT NULL COMMENT '币种',
    `app_dtm`            DATETIME(6) NULL DEFAULT NULL COMMENT '申请时间',
    `pmt_amt`            DECIMAL(19, 2) NULL DEFAULT NULL COMMENT '支付金额',
    `product_pay_end_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '产品打款截止日期',
    `product_pay_end_dm` VARCHAR(6) NULL DEFAULT NULL COMMENT '产品打款截止时间',
    `tx_pmt_flag`        VARCHAR(2) NULL DEFAULT NULL COMMENT '交易支付标记 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；',
    `pmt_comp_flag`      CHAR(1) NULL DEFAULT NULL COMMENT '支付对账标记0-无需对账；1-未对账；2-对账完成；3-对账不平;',
    `pmt_check_dt`       VARCHAR(8) NULL DEFAULT NULL COMMENT '支付对账日期',
    `out_pmt_deal_no`    VARCHAR(50) NULL DEFAULT NULL COMMENT '外部支付订单号',
    `pmt_org_code`       VARCHAR(30) NULL DEFAULT NULL COMMENT '支付机构代码',
    `ret_code`           VARCHAR(30) NULL DEFAULT NULL COMMENT '支付返回码',
    `ret_desc`           VARCHAR(1024) NULL DEFAULT NULL COMMENT '支付返回描述',
    `pmt_complete_dtm`   DATETIME(6) NULL DEFAULT NULL COMMENT '支付完成日期时间',
    `outlet_code`        VARCHAR(9) NULL DEFAULT NULL COMMENT '网点号',
    `trade_channel`      VARCHAR(2)  NOT NULL COMMENT '交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；',
    `memo`               VARCHAR(1024) NULL DEFAULT NULL COMMENT '备注',
    `order_type`         VARCHAR(2)  NOT NULL COMMENT '订单类型 1-交易 2-edda入金',
    `rec_stat`           CHAR(1)     NOT NULL DEFAULT '1' COMMENT '记录状态 0-无效 1-正常',
    `version`            INT(11) NULL DEFAULT '0' COMMENT '版本号',
    `create_timestamp`   DATETIME(6) NOT NULL COMMENT '创建时间戳',
    `update_timestamp`   DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_pmt_deal_no` (`pmt_deal_no`, `version`),
    INDEX                `idx_pmtorder_dealno` (`deal_no`),
    INDEX                `idx_pmtorder_pmt_check_dt` (`pmt_check_dt`),
    INDEX                `idx_pmtorder_updatedtm` (`update_timestamp`)
) COMMENT='海外支付订单'
;

```

## 海外支付对账结果

```sql
CREATE TABLE `hw_payment_check_result` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
	`pmt_deal_no` BIGINT(20) NOT NULL COMMENT '支付订单号',
	`deal_no` BIGINT(20) NULL DEFAULT NULL COMMENT '订单号',
	`hk_cust_no` VARCHAR(20) NOT NULL COMMENT '香港客户号',
	`fund_code` VARCHAR(6) NOT NULL COMMENT '基金代码',
	`currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '币种',
	`pmt_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '支付金额',
	`pmt_check_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '支付对账日期',
	`out_pmt_deal_no` VARCHAR(50) NULL DEFAULT NULL COMMENT '外部支付订单号',
	`out_pmt_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '外部支付金额',
	`out_currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '外部币种',
	`out_pmt_flag` VARCHAR(2) NULL DEFAULT NULL COMMENT '外部支付标识 1-待支付 2-支付成功 3-支付失败 4-支付中',
	`tx_pmt_flag` VARCHAR(2) NULL DEFAULT NULL COMMENT '交易支付标识 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中',
	`pmt_comp_flag` CHAR(1) NULL DEFAULT NULL COMMENT '支付对账标记0-无需对账；1-未对账；2-对账完成；3-对账不平;',
	`memo` VARCHAR(1024) NULL DEFAULT NULL COMMENT '备注',
	`rec_stat` CHAR(1) NOT NULL DEFAULT '1' COMMENT '记录状态 0-无效 1-正常',
	`version` INT(11) NULL DEFAULT 0 COMMENT '版本号',
	`create_timestamp` DATETIME(6) NOT NULL COMMENT '创建时间戳',
	`update_timestamp` DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
	PRIMARY KEY (`id`),
	UNIQUE INDEX `uk_pay_check_result_no` (`pmt_deal_no`, `version`),
	INDEX `idx_pay_check_result_dealno` (`deal_no`),
	INDEX `idx_pay_check_result_pmt_check_dt` (`pmt_check_dt`)
)
COMMENT='海外支付对账结果'
;
```

## 海外资金退款表

```sql
CREATE TABLE `hw_capital_refund` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `deal_no` BIGINT(20) NOT NULL COMMENT '订单号',
    `relational_deal_no` BIGINT(20) NULL DEFAULT NULL COMMENT '关联订单号(订单表的订单号)',
    `hk_cust_no` VARCHAR(20) NOT NULL COMMENT '香港客户号',
    `fund_tx_acct_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号',
    `fund_code` VARCHAR(6) NOT NULL COMMENT '基金代码',
    `fund_abbr` VARCHAR(200) NULL DEFAULT NULL COMMENT '基金简称',
    `fund_currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '基金币种',
    `fund_man_code` VARCHAR(18) NULL DEFAULT NULL COMMENT '管理人代码',
    `middle_busi_code` VARCHAR(5) NULL DEFAULT NULL COMMENT '中台业务码',
    `payment_type` VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式 1：电汇；2：支票；3：海外储蓄罐',
    `refund_direction` CHAR(1) NULL DEFAULT NULL COMMENT '退款方向 2-现金余额',
    `refund_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '退款金额',
    `refund_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '退款日期 YYYYMMDD',
	`handle_status` VARCHAR(1) NULL DEFAULT NULL COMMENT '处理状态 0-未处理 1-处理成功',
    `rec_stat` CHAR(1) NOT NULL DEFAULT '1' COMMENT '记录状态 0-无效 1-正常',
	`version` INT(11) NULL DEFAULT 0 COMMENT '版本号',
    `create_timestamp` DATETIME(6) NOT NULL COMMENT '创建时间戳',
    `update_timestamp` DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_capital_refund_deal_no` (`deal_no`, `version`),
    INDEX `idx_capital_refund_refund_dt` (`refund_dt`)
)
COMMENT='海外资金退款表';
```

## IO资金退款导出表

```sql
CREATE TABLE `io_capital_refund_export` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
	`export_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '导出日期',
	`file_id` BIGINT(20) NULL DEFAULT NULL COMMENT '文件id',
    `deal_no` BIGINT(20) NOT NULL COMMENT '订单号',
    `relational_deal_no` BIGINT(20) NULL DEFAULT NULL COMMENT '关联订单号(订单表的订单号)',
    `hk_cust_no` VARCHAR(20) NOT NULL COMMENT '香港客户号',
    `fund_tx_acct_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号',
    `fund_code` VARCHAR(6) NOT NULL COMMENT '基金代码',
    `fund_abbr` VARCHAR(200) NULL DEFAULT NULL COMMENT '基金简称',
    `fund_currency` VARCHAR(3) NULL DEFAULT NULL COMMENT '基金币种',
    `fund_man_code` VARCHAR(18) NULL DEFAULT NULL COMMENT '管理人代码',
    `busi_code` VARCHAR(5) NULL DEFAULT NULL COMMENT '业务码',
    `payment_type` VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式 1：电汇；2：支票；3：海外储蓄罐',
    `refund_direction` CHAR(1) NULL DEFAULT NULL COMMENT '退款方向 2-现金余额',
    `refund_amt` DECIMAL(19,2) NULL DEFAULT NULL COMMENT '退款金额',
    `refund_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '退款日期 YYYYMMDD',
    `create_timestamp` DATETIME(6) NOT NULL COMMENT '创建时间戳',
    `update_timestamp` DATETIME(6) NULL DEFAULT NULL COMMENT '更新时间戳',
    PRIMARY KEY (`id`),
    INDEX `idx_io_refund_deal_no` (`deal_no`),
    INDEX `idx_io_refund_file_id` (`file_id`),
    INDEX `idx_io_refund_export_dt` (`export_dt`)
)
COMMENT='io资金退款导出表';
```

## 海外储蓄罐导入申请表

```sql
ALTER TABLE `hw_piggy_trade_app_import`
	ADD UNIQUE INDEX `uk_import_app_id` (`import_app_id`, `version`),
	ADD COLUMN `fund_tx_acct_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '基金交易账号' AFTER `hk_cust_no`,
	CHANGE COLUMN `buy_amt` `buy_amt` DECIMAL(16,2) NULL DEFAULT NULL COMMENT '购买金额' AFTER `pre_submit_ta_dt`,
	ADD COLUMN `discount_rate` DECIMAL(8,4) NULL DEFAULT NULL COMMENT '折扣率' AFTER `fee`,
	CHANGE COLUMN `payment_type` `payment_type` VARCHAR(3) NULL DEFAULT NULL COMMENT '支付方式  1-电汇；2-支票；3-海外储蓄罐' AFTER `discount_rate`,
	CHANGE COLUMN `app_amt` `app_amt` DECIMAL(16,2) NULL DEFAULT NULL COMMENT '申请金额' AFTER `redeem_type`,
	CHANGE COLUMN `redeem_direction` `redeem_direction` VARCHAR(4) NULL DEFAULT NULL COMMENT '回款方向 1-回银行卡；2-留账好买香港账户；3-回海外储蓄罐；4-基金转投；' AFTER `app_vol`,
	CHANGE COLUMN `is_generated` `is_generated` VARCHAR(1) NULL DEFAULT NULL COMMENT '是否生成订单  0-未生成 1-已生成 2-生成失败' AFTER `import_dt`,
	ADD COLUMN `deal_no` VARCHAR(20) NULL DEFAULT NULL COMMENT '订单号' AFTER `is_generated`,
    ADD COLUMN `piggy_app_source` VARCHAR(20) NULL DEFAULT NULL COMMENT '储蓄罐申请来源 0-excel 1-可用余额、2-客户控管表、3-退款控管表、4-自动赎回' AFTER `deal_dtl_no`;

ALTER TABLE `hw_piggy_trade_app_import`
	DROP COLUMN `pay_end_dt`,
	DROP COLUMN `actual_pay_dt`,
	DROP COLUMN `actual_pay_amt`,
	DROP COLUMN `deal_dtl_no`;
```

## 海外订单明细表

```sql
ALTER TABLE `hw_deal_order_dtl`
ADD COLUMN `product_pay_end_dt` VARCHAR(8) NULL DEFAULT NULL COMMENT '产品打款截止日期' AFTER `pay_end_tm`,
ADD COLUMN `product_pay_end_dm` VARCHAR(6) NULL DEFAULT NULL COMMENT '产品打款截止时间' AFTER `product_pay_end_dt`;

```