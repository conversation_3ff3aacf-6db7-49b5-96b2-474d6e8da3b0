---
title: "海外交易-资金二期设计"
pageId: "101627637"
spaceKey: "lsm"
spaceId: "38600706"
author: "李少阳"
createdDate: "2025-07-25T09:56:29.000+08:00"
lastModified: "2025-07-25T09:56:29.000+08:00"
version: 207
confluenceUrl: "http://dms.intelnal.howbuy.com//pages/viewpage.action?pageId=101627637"
---
## 支付对账结果查询接口

-   请求地址

| 类名 | 请求方式 | 接口url | 描述 |
| --- | --- | --- | --- |
| QueryPaymentCheckResultFacade | dubbo | com.howbuy.dtms.order.client.facade.query.pay | 支付对账结果查询 |

-   入参

| 字段 | 字段注释 | 类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| pmtDealNo | 支付订单号 | String | 否 |  |
| dealNo | 订单号 | String | 否 |  |
| outPmtDealNo | 外部支付订单号 | String | 否 |  |
| fundCodes | 基金代码列表 | List<String> | 否 |  |
| pmtCheckDt | 支付对账日期 | String | 必填 |  |
| pmtCompFlags | 支付对账标记列表 | List<String> | 否 |  |
| pageNum | 页码 | Integer | 是 | 1 |
| pageSize | 每页大小 | Integer | 是 | 20 |

-   出参

| 字段 | 字段注释 | 类型 | 备注 |
| --- | --- | --- | --- |
| total | 总记录数 | Long |  |
| pageNum | 当前页码 | Integer |  |
| pageSize | 每页大小 | Integer |  |
| pages | 总页数 | Integer |  |
| list | 数据列表 | Array |  |
| pmtDealNo | 支付订单号 | Long |  |
| dealNo | 订单号 | Long |  |
| middleBusiCode | 中台业务代码 | String |  |
| paymentTypeList | 支付方式列表 | String | 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐 |
| hkCustNo | 香港客户号 | String |  |
| custName | 客户姓名 | String |  |
| cpAcctNo | 资金账号 | String |  |
| fundTxAcctNo | 基金交易账号 | String |  |
| fundCode | 基金代码 | String |  |
| fundAddr | 基金简称 | String |  |
| currency | 币种 | String |  |
| appDtm | 申请时间 | String |  |
| pmtAmt | 支付金额 | String |  |
| pmtCheckDt | 支付对账日期 | String |  |
| outPmtDealNo | 外部支付订单号 | String |  |
| outPmtAmt | 外部支付金额 | String |  |
| outCurrency | 外部币种 | String |  |
| outPmtFlag | 外部支付标识 | String | 1-待支付 2-支付成功 3-支付失败 4-支付中 |
| txPmtFlag | 交易支付标识 | String | 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款;6-等待付款；7-撤单成功； |
| pmtCompFlag | 支付对账标记 | String | 0-无需对账；1-未对账；2-对账完成；3-对账不平; |
| memo | 备注 | String |  |
| list | 数据列表 | Array |  |

-   处理逻辑
    -   根据入参分页查询支付订单表(hw\_payment\_order)
        -   关联支付对账结果表(hw\_payment\_check\_result)
            -   关联条件：pmt\_Deal\_No = pmt\_Deal\_No 
            -   返回字段取值：outPmtDealNo、outPmtAmt、outCurrency、outPmtFlag、memo 取 支付对账结果表(hw\_payment\_check\_result)的out\_Pmt\_Deal\_No、out\_Pmt\_Amt、out\_Currency、out\_Pmt\_Flag、memo
        -   关联交易订单表(hw\_deal\_order)
            -   关联条件：deal\_No = deal\_No
            -   返回字段取值：custName、fundAddr 取交易订单表(hw\_deal\_order)的 cust\_chinese\_name、product\_abbr 赋值
        -   查询条件为接口入参，根据入参是否必填编写查询条件

## 重置交易支付标识接口

-   请求地址

    | 类名 | 请求方式 | 接口url | 描述 |
    | --- | --- | --- | --- |
    | ResetTxPmtFlagFacade | dubbo | com.howbuy.dtms.order.client.facade.trade.pay | 重置支付标识接口 |

    | 字段 | 字段注释 | 类型 | 是否必填 | 备注 |
    | --- | --- | --- | --- | --- |
    | pmtDealNo | 支付订单号 | String | 是 |  |

    -   出参

    | 字段 | 字段注释 | 类型 | 备注 |
    | --- | --- | --- | --- |
    | code | 状态码 | String | 状态码0000表示成功 |
    | description | 描述信息 | String |  |

-   接口逻辑
    -   根据入参pmt\_deal\_no查询支付订单表-hw\_payment\_order
        -   校验支付订单PO-paymentOrderPO是否存在
        -   校验交易支付标识txPmtFlag是否等于4-付款中
        -   校验失败，则返回校验失败信息
    -   如果orderType=1-交易，则进行交易订单-hw\_deal\_order校验
        -   根据支付订单的订单号查询交易订单-hw\_deal\_order
            -   校验交易订单是否存在
            -   校验支付状态payStatus是否等于2-付款中、订单状态orderStatus是否为 1-申请成功
        -   校验失败，则返回校验失败信息
    -   校验支付系统是否存在订单
        -   调用支付外部服务(PayOuterService.queryPayResult)
        -   校验支付返回.pmtDealNo为空
        -   校验失败，则返回校验失败信息
    -   事务修改支付订单表-hw\_payment\_order、交易订单-hw\_deal\_order
        -   修改支付订单hw\_payment\_order：设置tx\_pmt\_flag为1-未付款、update\_timestamp为服务器时间
            -   乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态tx\_pmt\_flag='4'和查询时获得的update\_timestamp值。
            -   冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
        -   order\_type=1-交易
            -   修改交易订单hw\_deal\_order：设置pay\_status为1-未付款、update\_timestamp为服务器时间。
                -   乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态order\_status='1'、pay\_status='2'和查询时获得的update\_timestamp值。
                -   冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。