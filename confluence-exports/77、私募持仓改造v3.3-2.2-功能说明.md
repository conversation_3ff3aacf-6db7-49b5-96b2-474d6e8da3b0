---
title: "77、私募持仓改造v3.3"
pageId: "101628680"
spaceKey: "PC"
spaceId: "25526273"
author: "骆珍珍"
createdDate: "2025-08-08T15:48:17.000+08:00"
lastModified: "2025-08-08T15:48:17.000+08:00"
version: 228
confluenceUrl: "http://dms.intelnal.howbuy.com//pages/viewpage.action?pageId=101628680"
---
### 2.2 功能说明

| 图示 | 功能模块 | 功能说明 |
|  | 1、顶部区域 | 1、页面标题：好臻专区2、点击，返回上一个页面 |
|  | 2、公告栏 | 1、整体功能不变，UI样式有调整，参考UI调整 |
|  | 3、总资产区域 | 1、资产统计范围说明：@珍珍复核（1）资产范围：【分销渠道=好臻分销】的产品（2）持有的判断条件：取客户的一账通，获取高端中台持仓份额表【持仓份额>0】的好臻持仓产品 //此处待陆云复核取值逻辑（3） 买入待确认的判断条件：*在途订单：（1）【业务类型】=1120-认购、1122-申购（2）【订单状态】=申请成功（3）【支付标识】=付款成功（4）【交易状态】=未确认*购买净金额=【申请金额】-【申请手续费】2、指标明细：指标名称指标取值初始投资成本SUM（客户当前持仓中好臻分销私募股权产品的【初始投资成本(人民币)】）+买入待确认资产注：若持仓产品中存在外币投资成本，需按实时中间汇率换算人民币后再参与加总求和总回款SUM（客户当前持仓中好臻分销私募股权产品的【已发生回款(人民币)】）注：若持仓产品中存在外币投资成本，需按实时中间汇率换算人民币后再参与加总求和买入待确认资产即好臻在途资产范围：取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=成功 的交易记录的【净申购金额】。【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。（1）买入待确认订单笔数：满足以上资产范围的交易记录的笔数；（2）买入待确认资产金额=SUM(满足以上资产范围的交易记录 【净申购金额】）回款比例[新增]回款比例=总回款/初始投资成本//人民币中间价：取DB，包含各币种对应的人民币中间价、汇率日期；DB取值：取人民币汇率中间价表<st_main.t_st_gg_rmbhlzjj>的中间价【zjj】，代表1外币可兑换人民币的汇率，其中，dhbz（枚举值type=301186）对应不同币种；人民币汇率中间表接口返回：1外币=N人民币；外币换算成人民币金额=原币种金额*该币种的最新人民币中间价3、展示说明：（1）常规场景指标展示说明图示初始投资成本a、标题：初始投资成本（元）b、金额取值：展示{初始投资成本}c、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <初始投资成本 tab>买入待确认资产a、标题：含买入待确认b、内容：含买入待确认：{买入待确认资产金额}示例：含买入待确认：10,000.00c、指标显隐：若买入待确认资产>0，则展示买入待确认提示；若买入待确认资产≤0，则隐藏提示语；d、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示总回款a、标题：总回款（元）b、金额取值：展示{总回款}c、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <总回款 tab>回款比例a、标题：初始投资成本（元）b、金额取值：展示{回款比例}c、金额格式：百分比格式，四舍五入两位小数展示，不足则补0；如果比例为0，则直接显示0.00%；d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <回款比例 tab>（2）含买入待确认场景若【买入待确认资产】>01、总资产下方显示买入待确认提示文案：含{A}买入待确认：{买入待确认资产金额}2、示例：含买入待确认：1,000,000.00若【买入待确认资产】≤0买入待确认提示文案隐藏（3）计算中场景//股权产品不涉及此部分（4）小眼睛场景小眼睛的整体逻辑同私募基金持仓页面；同时需注意多个场景同时命中时的显示处理，如在“买入待确认”、“数据异常”、含有消息提醒区域场景下的兼容处理（5）“收益分析”入口//股权产品不涉及此部分（6）指标说明0、触发条件资产总览指标解释说明：点击资产总览字段右边，可打开总览指标说明弹窗1、指标显示说明字段名解释说明初始投资成本共包括2块内容：1、第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额2、第二块（相关说明）：分3段（1）第1段：1、若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。（2）第2段：2、若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。（3）第3段：3、初始投资成本内未计入买入手续费。总回款[新增]共包括2块内容：1、第一块（指标解释）：总回款为您当前持有的所有股权产品累计回款总额2、第二块（相关说明）：总回款金额不受您是否发生过转受让交易影响。回款比例[新增]共包括1块内容：1、第一块（指标解释）：回款比例 = 当前总回款/初始投资成本2、交互说明（1）弹窗顶部展示tab切换项，包含：初始投资成本、总回款、回款比例（2）默认定位：从哪个指标点击进入，则默认定位在哪个指标的tab下；可手动切换，不可取消选中（3）定位在哪个指标tab下，就展示对应的内容；弹框长度和高度固定，一页内容展示不下时，顶部tab和底部“我知道了”按钮中间区域的内容可上下滑动（4）弹窗可左右滑动，切换到不同指标的内容；左右滑动时，弹框高度和宽度固定（5）弹窗底部展示“我知道了”按钮：点击关闭弹窗；点击弹窗外部区域，弹窗不消失（7）会员等级功能点功能说明整体说明1、总资产区域背景根据当前用户身份，分为“常规版（红色）”和“会员版（黑金）”两种，其中“会员版”的用户有三种会员等级2、同时支持CMS配置将“会员版”切换成“常规版”取值说明1、会员版用户：不同会员等级的用户会被打上不同的用户标签，可参考如下（实现逻辑参考高端客户权益的专题页即可，可咨询@曹华楠，使用现成接口即可）（1）【臻享会员】：用户标签id：23301的用户（2）【私享会员】：用户标签id：23302的用户（3）【尊享会员】：用户标签id：23303的用户2、常规版用户：若用户不在以上任一标签中，则视作无会员等级各等级具体说明可参考会员权益专题页需求：16、会员权益专题页 - 高端互联网 - Confluence展示说明1、常规版用户：非通栏红色，总资产区域为红色，字体为红色2、会员版用户：通栏样式，总资产区域为黑色，字体为金色，显示会员标识+会员名称（1）【臻享会员】：  （2）【私享会员】：（3）【尊享会员】：配置说明1、整体说明：（1）支持“会员黑金背景”切换成“会员红色背景”，支持按用户维度+页面维度配置切换范围（2）若用户既是高端会员，又把背景配置成常规红色，显示上和常规红色有差异，差异如下：① 红色背景变成通栏模式② 显示会员标识区域，logo+会员名称（3）【臻享会员】： （2）【私享会员】：（3）【尊享会员】：2、配置路径：CMS-->移动广告位管理-->key值：hmjjhybjqh（1）广告位描述：好买基金会员背景切换（2）唯一key值：hmjjhybjqh（3）所属产品：好买基金3、用户范围：支持按单一用户一账通配置&支持按用户标签配置（1）一账通配置说明：① 取“描述”字段配置② 若配置多个一账通，用“，”（英文逗号）隔开（2）用户标签配置说明：使用默认的“标签配置”功能（包含&不包含逻辑）4、页面范围：（1）该广告位的背景控制不仅限私募持仓，我的、好臻专区、好买香港专区、收益分析（包含私募自己的和公募合并的两个收益分析页），同步此逻辑（2）支持按页面维度配置是否切换（3）配置说明：① 取“扩展字段1”字段配置② 配置“1”代表切换“我的”页面、配置“2”代表切换“私募持仓”页面、配置“3”代表切换“好臻专区”页面、配置“4”代表“好买香港专区”页面，配置“5”代表“收益分析页”页面，配置“ALL”代表以上所有页面均统一切换♦ 配置多个单页面，用“，”（英文逗号）隔开♦ 若即配单页面又配ALL，按ALL处理5、其他说明：若对应的“广告ID”下线，则对应的配置失效交互说明1、点击“会员标识（logo+名称）”，则进入会员权益专题页各等级具体说明可参考会员权益专题页需求：16、会员权益专题页 - 高端互联网 - Confluence | 指标名称 | 指标取值 | 初始投资成本 | SUM（客户当前持仓中好臻分销私募股权产品的【初始投资成本(人民币)】）+买入待确认资产注：若持仓产品中存在外币投资成本，需按实时中间汇率换算人民币后再参与加总求和 | 总回款 | SUM（客户当前持仓中好臻分销私募股权产品的【已发生回款(人民币)】）注：若持仓产品中存在外币投资成本，需按实时中间汇率换算人民币后再参与加总求和 | 买入待确认资产 | 即好臻在途资产范围：取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=成功 的交易记录的【净申购金额】。【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。（1）买入待确认订单笔数：满足以上资产范围的交易记录的笔数；（2）买入待确认资产金额=SUM(满足以上资产范围的交易记录 【净申购金额】） | 回款比例[新增] | 回款比例=总回款/初始投资成本 | 指标 | 展示说明 | 图示 | 初始投资成本 | a、标题：初始投资成本（元）b、金额取值：展示{初始投资成本}c、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <初始投资成本 tab> |  | 买入待确认资产 | a、标题：含买入待确认b、内容：含买入待确认：{买入待确认资产金额}示例：含买入待确认：10,000.00c、指标显隐：若买入待确认资产>0，则展示买入待确认提示；若买入待确认资产≤0，则隐藏提示语；d、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示 | 总回款 | a、标题：总回款（元）b、金额取值：展示{总回款}c、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <总回款 tab> | 回款比例 | a、标题：初始投资成本（元）b、金额取值：展示{回款比例}c、金额格式：百分比格式，四舍五入两位小数展示，不足则补0；如果比例为0，则直接显示0.00%；d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <回款比例 tab> | 若【买入待确认资产】>0 | 1、总资产下方显示买入待确认提示文案：含{A}买入待确认：{买入待确认资产金额}2、示例：含买入待确认：1,000,000.00 |  | 若【买入待确认资产】≤0 | 买入待确认提示文案隐藏 |  | 0、触发条件 | 资产总览指标解释说明：点击资产总览字段右边，可打开总览指标说明弹窗 | 1、指标显示说明 | 字段名解释说明初始投资成本共包括2块内容：1、第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额2、第二块（相关说明）：分3段（1）第1段：1、若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。（2）第2段：2、若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。（3）第3段：3、初始投资成本内未计入买入手续费。总回款[新增]共包括2块内容：1、第一块（指标解释）：总回款为您当前持有的所有股权产品累计回款总额2、第二块（相关说明）：总回款金额不受您是否发生过转受让交易影响。回款比例[新增]共包括1块内容：1、第一块（指标解释）：回款比例 = 当前总回款/初始投资成本 | 字段名 | 解释说明 | 初始投资成本 | 共包括2块内容：1、第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额2、第二块（相关说明）：分3段（1）第1段：1、若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。（2）第2段：2、若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。（3）第3段：3、初始投资成本内未计入买入手续费。 | 总回款[新增] | 共包括2块内容：1、第一块（指标解释）：总回款为您当前持有的所有股权产品累计回款总额2、第二块（相关说明）：总回款金额不受您是否发生过转受让交易影响。 | 回款比例[新增] | 共包括1块内容：1、第一块（指标解释）：回款比例 = 当前总回款/初始投资成本 | 2、交互说明 | （1）弹窗顶部展示tab切换项，包含：初始投资成本、总回款、回款比例（2）默认定位：从哪个指标点击进入，则默认定位在哪个指标的tab下；可手动切换，不可取消选中（3）定位在哪个指标tab下，就展示对应的内容；弹框长度和高度固定，一页内容展示不下时，顶部tab和底部“我知道了”按钮中间区域的内容可上下滑动（4）弹窗可左右滑动，切换到不同指标的内容；左右滑动时，弹框高度和宽度固定（5）弹窗底部展示“我知道了”按钮：点击关闭弹窗；点击弹窗外部区域，弹窗不消失 | 功能点 | 功能说明 | 整体说明 | 1、总资产区域背景根据当前用户身份，分为“常规版（红色）”和“会员版（黑金）”两种，其中“会员版”的用户有三种会员等级2、同时支持CMS配置将“会员版”切换成“常规版” | 取值说明 | 1、会员版用户：不同会员等级的用户会被打上不同的用户标签，可参考如下（实现逻辑参考高端客户权益的专题页即可，可咨询@曹华楠，使用现成接口即可）（1）【臻享会员】：用户标签id：23301的用户（2）【私享会员】：用户标签id：23302的用户（3）【尊享会员】：用户标签id：23303的用户2、常规版用户：若用户不在以上任一标签中，则视作无会员等级各等级具体说明可参考会员权益专题页需求：16、会员权益专题页 - 高端互联网 - Confluence | 展示说明 | 1、常规版用户：非通栏红色，总资产区域为红色，字体为红色2、会员版用户：通栏样式，总资产区域为黑色，字体为金色，显示会员标识+会员名称（1）【臻享会员】：  （2）【私享会员】：（3）【尊享会员】： | 配置说明 | 1、整体说明：（1）支持“会员黑金背景”切换成“会员红色背景”，支持按用户维度+页面维度配置切换范围（2）若用户既是高端会员，又把背景配置成常规红色，显示上和常规红色有差异，差异如下：① 红色背景变成通栏模式② 显示会员标识区域，logo+会员名称（3）【臻享会员】： （2）【私享会员】：（3）【尊享会员】：2、配置路径：CMS-->移动广告位管理-->key值：hmjjhybjqh（1）广告位描述：好买基金会员背景切换（2）唯一key值：hmjjhybjqh（3）所属产品：好买基金3、用户范围：支持按单一用户一账通配置&支持按用户标签配置（1）一账通配置说明：① 取“描述”字段配置② 若配置多个一账通，用“，”（英文逗号）隔开（2）用户标签配置说明：使用默认的“标签配置”功能（包含&不包含逻辑）4、页面范围：（1）该广告位的背景控制不仅限私募持仓，我的、好臻专区、好买香港专区、收益分析（包含私募自己的和公募合并的两个收益分析页），同步此逻辑（2）支持按页面维度配置是否切换（3）配置说明：① 取“扩展字段1”字段配置② 配置“1”代表切换“我的”页面、配置“2”代表切换“私募持仓”页面、配置“3”代表切换“好臻专区”页面、配置“4”代表“好买香港专区”页面，配置“5”代表“收益分析页”页面，配置“ALL”代表以上所有页面均统一切换♦ 配置多个单页面，用“，”（英文逗号）隔开♦ 若即配单页面又配ALL，按ALL处理5、其他说明：若对应的“广告ID”下线，则对应的配置失效 | 交互说明 | 1、点击“会员标识（logo+名称）”，则进入会员权益专题页各等级具体说明可参考会员权益专题页需求：16、会员权益专题页 - 高端互联网 - Confluence |
| 指标名称 | 指标取值 |
| 初始投资成本 | SUM（客户当前持仓中好臻分销私募股权产品的【初始投资成本(人民币)】）+买入待确认资产注：若持仓产品中存在外币投资成本，需按实时中间汇率换算人民币后再参与加总求和 |
| 总回款 | SUM（客户当前持仓中好臻分销私募股权产品的【已发生回款(人民币)】）注：若持仓产品中存在外币投资成本，需按实时中间汇率换算人民币后再参与加总求和 |
| 买入待确认资产 | 即好臻在途资产范围：取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=成功 的交易记录的【净申购金额】。【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。（1）买入待确认订单笔数：满足以上资产范围的交易记录的笔数；（2）买入待确认资产金额=SUM(满足以上资产范围的交易记录 【净申购金额】） |
| 回款比例[新增] | 回款比例=总回款/初始投资成本 |
| 指标 | 展示说明 | 图示 |
| 初始投资成本 | a、标题：初始投资成本（元）b、金额取值：展示{初始投资成本}c、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <初始投资成本 tab> |  |
| 买入待确认资产 | a、标题：含买入待确认b、内容：含买入待确认：{买入待确认资产金额}示例：含买入待确认：10,000.00c、指标显隐：若买入待确认资产>0，则展示买入待确认提示；若买入待确认资产≤0，则隐藏提示语；d、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示 |
| 总回款 | a、标题：总回款（元）b、金额取值：展示{总回款}c、金额格式：千分位金额格式，直接截断保留2位小数，不足则补0；如果总资产为0，则直接显示0.00；内容要显示全，若一行展示不下则换行展示d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <总回款 tab> |
| 回款比例 | a、标题：初始投资成本（元）b、金额取值：展示{回款比例}c、金额格式：百分比格式，四舍五入两位小数展示，不足则补0；如果比例为0，则直接显示0.00%；d、指标解释说明：点击标题右边，可打开指标说明弹窗，并定位到 <回款比例 tab> |
| 若【买入待确认资产】>0 | 1、总资产下方显示买入待确认提示文案：含{A}买入待确认：{买入待确认资产金额}2、示例：含买入待确认：1,000,000.00 |  |
| 若【买入待确认资产】≤0 | 买入待确认提示文案隐藏 |  |
| 0、触发条件 | 资产总览指标解释说明：点击资产总览字段右边，可打开总览指标说明弹窗 |
| 1、指标显示说明 | 字段名解释说明初始投资成本共包括2块内容：1、第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额2、第二块（相关说明）：分3段（1）第1段：1、若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。（2）第2段：2、若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。（3）第3段：3、初始投资成本内未计入买入手续费。总回款[新增]共包括2块内容：1、第一块（指标解释）：总回款为您当前持有的所有股权产品累计回款总额2、第二块（相关说明）：总回款金额不受您是否发生过转受让交易影响。回款比例[新增]共包括1块内容：1、第一块（指标解释）：回款比例 = 当前总回款/初始投资成本 | 字段名 | 解释说明 | 初始投资成本 | 共包括2块内容：1、第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额2、第二块（相关说明）：分3段（1）第1段：1、若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。（2）第2段：2、若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。（3）第3段：3、初始投资成本内未计入买入手续费。 | 总回款[新增] | 共包括2块内容：1、第一块（指标解释）：总回款为您当前持有的所有股权产品累计回款总额2、第二块（相关说明）：总回款金额不受您是否发生过转受让交易影响。 | 回款比例[新增] | 共包括1块内容：1、第一块（指标解释）：回款比例 = 当前总回款/初始投资成本 |
| 字段名 | 解释说明 |
| 初始投资成本 | 共包括2块内容：1、第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额2、第二块（相关说明）：分3段（1）第1段：1、若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。（2）第2段：2、若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。（3）第3段：3、初始投资成本内未计入买入手续费。 |
| 总回款[新增] | 共包括2块内容：1、第一块（指标解释）：总回款为您当前持有的所有股权产品累计回款总额2、第二块（相关说明）：总回款金额不受您是否发生过转受让交易影响。 |
| 回款比例[新增] | 共包括1块内容：1、第一块（指标解释）：回款比例 = 当前总回款/初始投资成本 |
| 2、交互说明 | （1）弹窗顶部展示tab切换项，包含：初始投资成本、总回款、回款比例（2）默认定位：从哪个指标点击进入，则默认定位在哪个指标的tab下；可手动切换，不可取消选中（3）定位在哪个指标tab下，就展示对应的内容；弹框长度和高度固定，一页内容展示不下时，顶部tab和底部“我知道了”按钮中间区域的内容可上下滑动（4）弹窗可左右滑动，切换到不同指标的内容；左右滑动时，弹框高度和宽度固定（5）弹窗底部展示“我知道了”按钮：点击关闭弹窗；点击弹窗外部区域，弹窗不消失 |
| 功能点 | 功能说明 |
| 整体说明 | 1、总资产区域背景根据当前用户身份，分为“常规版（红色）”和“会员版（黑金）”两种，其中“会员版”的用户有三种会员等级2、同时支持CMS配置将“会员版”切换成“常规版” |
| 取值说明 | 1、会员版用户：不同会员等级的用户会被打上不同的用户标签，可参考如下（实现逻辑参考高端客户权益的专题页即可，可咨询@曹华楠，使用现成接口即可）（1）【臻享会员】：用户标签id：23301的用户（2）【私享会员】：用户标签id：23302的用户（3）【尊享会员】：用户标签id：23303的用户2、常规版用户：若用户不在以上任一标签中，则视作无会员等级各等级具体说明可参考会员权益专题页需求：16、会员权益专题页 - 高端互联网 - Confluence |
| 展示说明 | 1、常规版用户：非通栏红色，总资产区域为红色，字体为红色2、会员版用户：通栏样式，总资产区域为黑色，字体为金色，显示会员标识+会员名称（1）【臻享会员】：  （2）【私享会员】：（3）【尊享会员】： |
| 配置说明 | 1、整体说明：（1）支持“会员黑金背景”切换成“会员红色背景”，支持按用户维度+页面维度配置切换范围（2）若用户既是高端会员，又把背景配置成常规红色，显示上和常规红色有差异，差异如下：① 红色背景变成通栏模式② 显示会员标识区域，logo+会员名称（3）【臻享会员】： （2）【私享会员】：（3）【尊享会员】：2、配置路径：CMS-->移动广告位管理-->key值：hmjjhybjqh（1）广告位描述：好买基金会员背景切换（2）唯一key值：hmjjhybjqh（3）所属产品：好买基金3、用户范围：支持按单一用户一账通配置&支持按用户标签配置（1）一账通配置说明：① 取“描述”字段配置② 若配置多个一账通，用“，”（英文逗号）隔开（2）用户标签配置说明：使用默认的“标签配置”功能（包含&不包含逻辑）4、页面范围：（1）该广告位的背景控制不仅限私募持仓，我的、好臻专区、好买香港专区、收益分析（包含私募自己的和公募合并的两个收益分析页），同步此逻辑（2）支持按页面维度配置是否切换（3）配置说明：① 取“扩展字段1”字段配置② 配置“1”代表切换“我的”页面、配置“2”代表切换“私募持仓”页面、配置“3”代表切换“好臻专区”页面、配置“4”代表“好买香港专区”页面，配置“5”代表“收益分析页”页面，配置“ALL”代表以上所有页面均统一切换♦ 配置多个单页面，用“，”（英文逗号）隔开♦ 若即配单页面又配ALL，按ALL处理5、其他说明：若对应的“广告ID”下线，则对应的配置失效 |
| 交互说明 | 1、点击“会员标识（logo+名称）”，则进入会员权益专题页各等级具体说明可参考会员权益专题页需求：16、会员权益专题页 - 高端互联网 - Confluence |
|  | 4、icon区域 | 1、取值说明：配置无调整，原取值逻辑如下供参考（1）导航栏取值说明：取值范围：取cms-广告位管理-移动广告位管理-hzccicon(好臻持仓)下的上线的icon广告个数：无限制排序：根据广告位顺序升序，按行从左往右排序，1行4个。（2）单个icon配置：icon名：取广告标题字段icon图：取图片字段，尺寸：150px * 150px （宽x高)跳转链接：取链接地址字段，支持原生短命令与H5链接的跳转。特别说明：若icon配置链接地址为原生短命令，则h5远程访问私募持仓首页时，隐藏该icon；con排序：按广告位配置顺序，前端从左往右升序排列，一行4个拓展字段1：a）若【拓展字段1】=1，则前端需校验客户是否存在定投合约① 若存在，则替换该icon为“我的定投”；② 若不存在，则正常展示该icon；b）若【拓展字段1】=0或空，则前端无需校验客户是否存在定投合约，正常展示该iconc）特别说明：若存在多个icon的【拓展字段1】=1，则根据排序对最后一个进行上述校验和替换。用户标签控制：取【配置标签】中设置的用户标签，可以选择【包含】和【不包含】关系【包含】则仅展示给所选标签下用户【不包含】则剔除展示所选标签下用户若同一个用户，同时存在于【包含】和【不包含】的标签中，则优先级：不包含＞包含补充：新增三个配置项（版本控制+icon角标+iconID）（1）版本控制：取“投放版本控制”字段，支持配置APP版本号① “~版本号”代表小于等于该版本号的版本可见② “版本号~”代表大于等于该版本号的版本可见③ “版本号1~版本号2”代表大于等于版本号1且小于等于版本号2的可见// 风险点：臻财VIP-H5版本持仓页不受APP版本控制，icon配置版本不生效，均可见方案：待开发评估是否支持 非APP环境只取8.9.9版本的icon配置项（2）icon角标：取“扩展字段2”配置内容① 前端控制最多不超过6个字，超过直接截断处理（3）iconID：取“扩展字段3”配置内容2、展示交互：本次调整：icon区域固定一行展示，一屏最多展示4个icon，若超过4个底部出现进度条，支持滑动切换下一屏icon（注：滑动是分屏切换，即4个一组切换）3、预设icon：风险测评、合同文件、产品报告、预约/双录、交易记录。//各预设icon点击后页面交互如下：icon名称交互说明风险测评（1）产线已有页面，本期无调整；（2）点击后进入产线已有的风险测评页面：a、若客户好臻风险测评已过期，则展示<好臻风险测评（已过期）>页面b、若客户好臻风险测评未过期，则展示<好臻风险测评结果>页面合同文件（1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_合同文件需求（2）点击后进入合同文件页面，该页面不含有筛选功能，因此展示全量合同文件即可（含好臻产品）；产品报告（1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_产品报告需求（2）点击后进入产品报告页面，该页面不含有筛选功能，因此展示全量产品报告即可（含好臻产品）；预约/双录（1）产线已有页面，本期无调整；（2）点击后进入产线已有的预约/双录页面，该页面不含有筛选功能，因此展示全量预约/双录数据即可（含好臻产品）：交易记录（1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_交易记录需求（2）点击后进入交易记录列表页，默认定位<好臻Tab>，可点击列表交易记录查看交易记录详情； | icon名称 | 交互说明 | 风险测评 | （1）产线已有页面，本期无调整；（2）点击后进入产线已有的风险测评页面：a、若客户好臻风险测评已过期，则展示<好臻风险测评（已过期）>页面b、若客户好臻风险测评未过期，则展示<好臻风险测评结果>页面 | 合同文件 | （1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_合同文件需求（2）点击后进入合同文件页面，该页面不含有筛选功能，因此展示全量合同文件即可（含好臻产品）； | 产品报告 | （1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_产品报告需求（2）点击后进入产品报告页面，该页面不含有筛选功能，因此展示全量产品报告即可（含好臻产品）； | 预约/双录 | （1）产线已有页面，本期无调整；（2）点击后进入产线已有的预约/双录页面，该页面不含有筛选功能，因此展示全量预约/双录数据即可（含好臻产品）： | 交易记录 | （1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_交易记录需求（2）点击后进入交易记录列表页，默认定位<好臻Tab>，可点击列表交易记录查看交易记录详情； |
| icon名称 | 交互说明 |
| 风险测评 | （1）产线已有页面，本期无调整；（2）点击后进入产线已有的风险测评页面：a、若客户好臻风险测评已过期，则展示<好臻风险测评（已过期）>页面b、若客户好臻风险测评未过期，则展示<好臻风险测评结果>页面 |
| 合同文件 | （1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_合同文件需求（2）点击后进入合同文件页面，该页面不含有筛选功能，因此展示全量合同文件即可（含好臻产品）； |
| 产品报告 | （1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_产品报告需求（2）点击后进入产品报告页面，该页面不含有筛选功能，因此展示全量产品报告即可（含好臻产品）； |
| 预约/双录 | （1）产线已有页面，本期无调整；（2）点击后进入产线已有的预约/双录页面，该页面不含有筛选功能，因此展示全量预约/双录数据即可（含好臻产品）： |
| 交易记录 | （1）产线已有页面，本期优化页面展示，本期该页面改造需求详见：82、交易工具相关（交易记录、补签协议、产品报告等）_交易记录需求（2）点击后进入交易记录列表页，默认定位<好臻Tab>，可点击列表交易记录查看交易记录详情； |
|  | 5、提醒区域 | 1、统计范围：仅统计“好臻分销”下的数据2、事项范围：在途交易提醒、资金即将到账提醒、补签协议提醒3、取值逻辑：（1）分销渠道：根据产品代码，获取DB<产品基本信息表>的【销售机构xsjg】字段好买分销：【销售机构xsjg】字段，不等于“03”或“05”都是好买分销好臻分销：【销售机构xsjg】字段等于“05”，即为好臻分销香港分销：【销售机构xsjg】字段等于“03”，即为香港代销（2）在途交易笔数：取高端中台，客户好臻产品对应的买入在途订单，即满足如下条件的订单a、买入在途订单包括：待付款订单数 + 已付款待确认订单数，即买入未确认的全部订单① 待付款订单数：【交易类型】=认购/申购，【订单状态】=申请成功，【付款状态】=未付款/付款中 的交易记录② 已付款待确认订单数：【交易类型】=认购/申购，【订单状态】=申请成功，【付款状态】=成功 的交易记录（3）即将到账资金笔数：取高端中台该客户好臻产品对应的待退款+待回款订单数，即满足如下条件的订单：待退款订单数：取同时满足以下两个条件的订单笔数条件1：订单已付款，但 自行撤销/强制撤销/确认失败/部分确认/申请失败(仅兜底)，导致需退款（仅代销，无直销）a）取好买代销产品：【交易类型】=认购/申购 ，【付款状态】=成功 ，【订单状态】=自行撤销/强制撤销/申请失败(仅兜底) ，【回款方向】=回银行卡 的交易记录 //撤单退款回储蓄罐是类实时的，产线没有做提醒b）取好买代销产品：【交易类型】=认购/申购 ，【付款状态】=成功 ，【订单状态】=部分确认/确认失败 ，【回款方向】=回储蓄罐/回银行卡 的交易记录补充说明：（1）客户已支付的买入订单，撤单之后想买其他产品，前端留了一个口子，可以选转投其他基金（就是不用先退款，客户再重新转账，减少操作）如果撤单时选转投其他基金，这笔退款方向就是回“可用余额”，客户转投其他基金的订单，重新做资金匹配就是直接用“可用余额”不用客户划款需求地址：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=29625699（2）可用余额客户看不到，但是每天会给OP有可用余额的客户清单，让OP去问客户这个钱要留着买产品还是直接退到他银行卡条件2：需退款订单资金未出款 或者 出款不足1天（仅好买代销，无直销）① 若【资金状态】=未出款，且（【当前日期】-【撤单日期】）＜ 3 个工作日，计为待退款订单注：资金状态是中台包的可能不准，一般退款不会超过3个工作日还不出款，产线已有限制② 若【资金状态】=已出款，且（【当前时间】-【出款时间】）＜ 1 个工作日，计为待退款订单待回款订单数：取同时满足以下两个条件的订单笔数条件1：订单已确认成功，需接受好买打款a）好买代销产品：【交易类型】=赎回/现金分红/股权回款/强赎/基金清盘/基金终止/到期收益/到期赎回，【订单状态】=全部确认/部分确认，【确认金额】>0 的交易记录b）直销产品：取【交易类型】=赎回/现金分红/股权回款/强赎/基金清盘/基金终止/到期收益/到期赎回，【订单状态】=确认成功，【确认金额】>0 的交易记录补充说明：直销产品暂不考虑 部分赎回失败 的场景条件2：需打款订单正常，且资金未出款 或者 出款不足1天a）好买代销产品，即有出款状态：① 若【异常赎回标识】≠不出款，且【资金状态】=未出款，且（【当前日期】-【确认日期】）＜15个工作日，计为待回款订单注：资金状态是中台包的可能不准，一般回款不会超过15个工作日还不出款，产线已有限制② 若【异常赎回标识】≠不出款，且【资金状态】=已出款 ，且（【当前时间】-【出款时间】）＜1个工作日，计为待回款订单③ 若【异常赎回标识】=不出款，则不计入待回款订单b）若为直销产品，无出款状态，主观定义提醒时长① 若【订单状态】=确认成功，则（【当前时间】-【确认时间】）＜3个工作日 时，计为待回款订单补充说明：若主订单拆单，任一明细订单满足上述条件，都要计入，且按主订单维度计入a）主订单下多笔订单明细如果有任何一条【资金状态】不是已出款，计入【购买待退款订单数】或【赎回待回款订单数】b）主订单下多笔订单明细如果有任何一条【资金状态】为已出款，且【出款日期】为当前自然日，计入【购买待退款订单数】或【赎回待回款订单数】（4）即将到账资金：取客户好臻产品待退款订单 + 待回款订单 退款/回款金额总和，即上述（3）到的资金明细 SUM（出款金额）注：若出款币种不是人民币，默认根据最新汇率转化成人民币金额，再做汇总（5）补签协议产品个数：取客户好臻分销产品待签署的补签协议，按产品去重汇总计数只取产品是好买分销下的产品，且客户仍持仓补签协议只取状态为“待签署”且未过期 (即【签订截止时间】<= 当前时间) 的补签协议4、交互说明（0）通用说明：消息展示默认顺序，按照：在途交易提醒、资金即将到账提醒、补签协议提醒模块底部提供关闭icon //所有的在途提醒和待办任务提醒，均支持关闭a）显隐控制：【在途交易数】> 1 或 【在途资金提醒】> 1 或 【补签协议】> 1时展示 ，否则隐藏iconb）点击icon，整个提醒区域关闭，关闭后保持隐藏，重新进入页面时再次展示（1）在途交易笔数提醒若【在途交易笔数】= 1，展示提示语“当前有{在途交易笔数}笔交易进行中”，点击跳转该笔交易记录的<交易记录详情页>若【在途交易笔数】＞1，展示提示语“当前有{在途交易笔数}笔交易进行中”，点击跳转<交易记录列表页>的“在途tab”若【在途交易笔数】= 0，隐藏提示语（2）资金即将到账提醒若【即将到账资金笔数】= 1，展示提示语“预计{即将到账资金笔数}笔资金即将到账，到账金额{即将到账资金}(仅供参考，以实际到账为准若已到账请忽略此提醒)”a）其中，【即将到账资金】千分位展示，截断保留2位小数，默认单位“元”b）点击提醒区域，跳转该笔交易记录的<交易记录详情页>若【即将到账资金笔数】＞1，展示提示语“预计{即将到账资金笔数}笔资金即将到账，到账金额{即将到账资金}(仅供参考，以实际到账为准；若已到账请忽略此提醒)”，a）其中，【即将到账资金】千分位展示，截断保留2位小数，默认单位“元”b）点击提醒区域，跳转<待回款订单页>，默认只展示待回款的订单i）页面标题：待回款订单ii）返回icon：点击icon，返回上一页iii）交易记录列表：样式&字段取值参考<交易记录列表页>，点击交易记录，跳转<交易记录详情页>若【即将到账资金笔数】= 0，隐藏提示（3）补签协议提醒若【补签协议产品个数】= 1，展示提示语“你持有的{补签协议产品个数}个产品有协议需要签订”，点击跳转该补签协议的<补签协议签订页>若【补签协议产品个数】＞1，展示提示语“你持有的{补签协议产品个数}个产品有协议需要签订”，点击跳转<补签协议列表页>若【补签协议产品个数】= 0，隐藏提示(补签协议签订页) (补签协议签订页) |
|  | 6、banner区域 | 1、取值逻辑：（1）广告位取值说明：① 取值范围：取cms-广告位管理-移动广告位管理-hzzqbanner下的上线的广告② 广告个数：未限制③ 排序：根据广告顺序升序排列（2）单个广告配置：① 标签配置：取【配置标签】中设置的用户标签，可以选择【包含】和【不包含】关系1）【包含】则仅展示给所选标签下用户2）【不包含】则剔除展示所选标签下用户3）若同一个用户，同时存在于【包含】和【不包含】的标签中，则优先级：不包含＞包含② 链接地址：支持APP短链和外链；③ banner图：取【iPhone】&【Android】字段，尺寸：待UI提供//icon图需要支持配置图片和gif动态图2、交互说明：（1）点击跳转链接对应地址（2）广告位交互说明：① 若banner个数＞1，banner底部展示轮播点，5S轮播一个，轮播动画为平滑左移，支持左右滑动；② 若banner个数≤1，banner底部轮播点隐藏；③ 若banner个数=0，即不存在banner，隐藏banner及轮播点； |
|  | 7、持仓明细区域 | 1、整体说明：（1）本期优化：原产线仅展示对持仓产品展示产品卡片，本期针对于在途产品，即首次买入待确认的产品，增加资产卡片展示（2）模块标题：持仓明细（3）模块功能划分：筛选条件区域、产品卡片、已清仓产品入口（4）场景拆分：场景图示功能说明1、无好臻持仓1、模块标题：显示2、筛选条件区：隐藏3、内容区：（1）显示无内容图标（2）提示语：当前没有持仓资产， 去【私募股权】看看吧 ＞① “私募股权”标红处理② 点击整行文字，跳转到“私募股权”页面2、有好臻持仓1、模块标题：显示2、筛选条件区：展示，功能详见下述3、内容区：显示持仓产品卡片，功能详见下述说明4、已清仓产品：若有已清仓产品，则展示入口，功能详见下述3、全部已清仓1、模块标题：显示2、筛选条件区：隐藏3、内容区：（1）显示无内容图标（2）提示语：当前没有持仓资产， 去【私募股权】看看吧 ＞① “私募股权”标红处理② 点击整行文字，跳转到“私募股权”页面4、已清仓产品：展示入口，功能详见下述2、取值逻辑：展示该客户所有持有+买入待确认的好臻产品明细 （分销渠道=好臻分销）@珍珍复核（1）买入待确认的判断条件：①【交易类型】=1120-认购、1122-申购②【订单状态】=申请成功③【支付标识】=付款成功④【交易状态】=未确认a、买入待确认笔数=满足以上条件的交易记录订单数；b、买入待确认金额=SUM（资产范围内全部产品的买入待确认交易记录的【净申购金额】）；（2）持有的判断条件：//好臻为代销产品，走代销逻辑①代销产品：【持仓份额】＞0（3）单产品始投资成本：取客户一账通号码+产品中台实时计算产品的【初始投资成本(人民币)】，产线逻辑保持不变；（4）单产品已回款：取客户一账通号码+产品代码查询资产中心，获取产品的【已发生回款(人民币)】；（5）单产品期限说明：取产品代码查询DB，获取产品的【产品期限说明】；代码取值：前端取的中台接口的fundCXQXStr字段-》中台取的非交易接口：com.howbuy.simu.service.base.product.SmccProductJbxxService#getSmccFromCacheByCodes 字段cxqxStr 入参是基金代码-》非交易：取的是DB的3个字段，拼好数据返回给中台使用的，会拼接+号和单位“年”，示例：4+4+2年；（6）单产品产品简称：取产品代码查询DB，获取产品的【产品简称】（7）单产品买入待确认：订单条件：【交易类型】=认购/申购 &【订单状态】=1-申请成功 &【付款状态】=4-成功a、买入待确认笔数=满足以上条件的交易记录订单数；b、买入待确认金额=SUM（资产范围内全部产品的买入待确认交易记录的【净申购金额】）；（8）单产品股权即将回款：单产品汇总统计满足以下条件的订单的【确认金额】条件1：订单已确认成功：【交易类型】=124赎回、143红利发放、142强赎、150基金清盘、151基金终止、186 到期收益、187到期赎回 & 【订单状态】=2-部分确认/3-确认成功 的交易记录；条件2：该笔需打款订单：① 若【异常赎回标识】≠不出款，且【资金状态】=未出款，计为待回款订单；② 若【异常赎回标识】≠不出款，【资金状态】=已出款 ，且（【当前时间】-【出款时间】）＜1个工作日，计为待回款订单；（9）清算中标签：查询高端中端，获取产品的清盘中标识(若crisisFlag=0，则未清盘)3、筛选条件区域：（1）筛选项支持：按初始投资成本排序，即按照字段{好臻持仓产品始投资成本}内容排序（升序/降序以页面勾选为准）按回款排序，即按照字段{好臻持仓产品已回款}内容排序（升序/降序以页面勾选为准）进入页面时，默认按照初始投资成本排序（2）排序规则：包括 降序、升序默认显示降序按钮，按排序选项 降序排点击一次排序按钮，按钮变为升序，按排序选项 升序排再次点击排序按钮，按钮恢复降序按钮，按排序选项 降序排4、产品卡片内容区域：（1）整体说明：（基本同私募持仓页的股权卡片）产品卡片部分字段维持产线，部分字段需根据 ② 场景条件说明中买入待确认/资金即将到账 的不同场景区分显示 （私募持仓列表页部分定义的场景）买入待确认：单次首次买入：原产线首次买入无资产卡片，本次需新增（有变动，请注意）产品卡片的分红标识/计算中/清算中标志维持产线，本次无调整以下会罗列全部涉及的指标字段（若对比产线有文档遗漏的字段，请找产品补充）本次新增的在途提醒区域，若无提醒数据，样式需兼容，则区域隐藏处理，不要多出来空白间隙点击单产品卡片，进入对应产品的资产详情页（2）卡片明细说明：字段字段说明图示（无回款）（有回款）基金简称【维持产线】取值和显示逻辑不变产品策略【新增】1、取值说明：（1）产品策略：取持仓产品对应的“6+1”策略a.若为大类配置FOF，则策略对应为“多策略”是否大类配置FOF：取私募基金附加信息表<st_hedge.t_st_sm_jjxx>，限定【fof二级分类 fofejfl in (104,105,106)】剔除m_opt_type=03 的产品，取到的即为大类配置fofb.否则，取产品对应的资配一级策略，包括 股票型、股权型、CTA、另类、固收与中性根据【基金代码】jjdm，取高端标签表<st_high.t_st_gd_bq>，限制[bqdx=1]，[bqdl='301651']，[jzrq='29991231']，取【标签小类】bqxl字段，值为枚举值，枚举type=3016512、显示说明：（1）在标题下面以标签形式展示，和标题左对齐；对应产品取不到策略值，标签隐藏初始投资成本【调整】1、取值说明：（1）初始投资成本= 当前初始投资成本（维持产线）+ 买入待确认金额（本次新增）//注：买入待确认-首次买入场景：原初始投资成本=0，初始投资成本仅买入待确认部分（2）当前初始投资成本，维持产线，公式不变（3）买入待确认金额=当前产品的总净买入金额 - 当前产品的好基储蓄罐预支付冻结金额代销产品：若【是否计入OP】=是，则取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=4-成功 的交易记录的【净申购金额】，【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。若【申请费用】为空，则按0处理直销产品：若【是否计入OP】=否，取CRM中【录入时间】≥2022/11/1 & 【交易类型】=认购/申购 &【预约状态】=已确认 &【订单状态】=申请成功(未确认) &【打款状态】=到账确认 的预约记录的【实际打款金额】；若为外币，需按实时汇率接口转化为人民币储蓄罐预支付冻结金额：取支付方式为储蓄罐的在途订单，关联的储蓄罐预约冻结金额1、取值来源：（1）在途资产总额：取自中台接口 simu/user/balance 的 【总待确认金额-totalUnconfirmedAmt】（2）目前，中台的【总待确认金额】取份额表(cust_books)、在途表(cust_books_dtl)、crm交易确认表(CM_CUSTTRADE_DIRECT)以下字段的加总值：代销产品：份额表的【未确认金额uncomfirmed_amt】+在途表的【申请金额app_amt】直销产品：筛选出crm交易确认表的中【订单状态-ORDERSTATE】=1-申请成功，且【交易类型-busicode】=120-认购/122-申购的交易记录。取符合条件的交易记录的【申请金额-appamt】加总值；（3）补充说明：在途表和份额表是中台的表，记录代销产品的未确认金额在途表记录最新的(T-1日)的未确认金额，在T日会跑批将这份数据落到份额表；在途表和订单表关联，没有判断交易类型，但是只有认购/申购会涉及到未确认金额份额表记录除“在途表”以外的未确认金额；份额表是个总表，没有和具体的订单相关联CRM的交易确认表，记录直销产品的未确认金额，只取订单状态=1 申请成功，交易类型=认购/申购 的未确认金额（4）显示说明：同产线标题：初始投资成本(单位)；此处的单位以服务端返回的为准，可能会出现外币单位；如 投资成本(元)/ 投资成本(美元)投资成本：金额格式，四舍五入保留2位小数，如果是外币不用换算，直接显示原金额；如 1,000,000.002、解释弹窗调整说明：@待确认（1）弹窗显示说明：使用本次弹窗新样式展示指标说明（2）指标说明：共包括2块内容第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额第二块（相关说明）：分3段第1段：若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。第2段：若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。第3段：初始投资成本内未计入买入手续费。产品期限说明【调整】1、取值说明：当股权产品回款<=0时，显示产品期限说明字段，本次在产线的基础上优化显示逻辑调整后：（1）产品期限说明：｛投资期｝+｛退出期｝+｛延长期｝年，示例：“2+2+2年”；此处根据产品实际配置进行展示，若只维护了投资期和退出期，则只显示｛投资期｝+｛退出期｝年，示例：“2+2年”（2）字段右侧显示说明图标，点击之后弹出小气泡，小气泡显示：“投资期+退出期+延长期”，点击小气泡本身或页面任一位置，小气泡消失；小气泡内容，根据实际下发的阶段机动展示，如产品只维护了投资期和退出期，则只显示“投资期＋退出期”产线取值逻辑：：（1）前端取的中台接口的fundCXQXStr字段（2）中台取的非交易接口：com.howbuy.simu.service.base.product.SmccProductJbxxService#getSmccFromCacheByCodes 字段cxqxStr 入参是基金代码（3）非交易：取的是DB的3个字段，拼好数据返回给中台使用的，会拼接+号和单位“年”，示例：4+4+2年；* 投资期private String tzq;* 退出期private String tcq;* 延长期private String ycq;2、前端判断逻辑说明：（1）若产品的清盘中标识=是(crisisFlag=1)，则产品期限说明显示：--（2）若产品的清盘中标识=否(crisisFlag=0)，则展示产品期限说明，示例：4+4+2年2、显示说明（1）原产线的提示文案的交互优化：点击小图标后出现的气泡，点击除气泡内容外的任意位置消失（2）提示内容维持产线不变已回款【维持产线】取值和显示逻辑不变在途提示语区域【新增】1、按场景显示不同的提示语，若同时击中多种场景，均需显示（1）消息展示优先级：根据提醒事件发生顺序依次展示（2）提醒区域，支持5S轮换下一个提醒事件，循环轮播，同时支持手动滑动切换2、当前产品含场景【买入待确认（包括首次买入或追加买入）】（1）在初始投资成本下方显示提示语，直到对应订单交易确认，提醒消失（2）文案模板：含买入待确认：「买入待确认金额+币种」；示例：“含买入待确认：1,000,000.00元”，若一行显示不下换行显示// 买入待确认金额计算同初始投资成本处描述3、当前产品含场景【资金即将到账：退款到账】时：（1）标签：退款即将到账（2）若【回款方向】=”银行卡“ 或 回款方向为空：显示文案：退款至银行卡，金额「待退款订单退款总额+单位」金额单位：取币种文案示例：退款至银行卡，金额：1,000.00元若一行显示不下换行显示，显示完整为止（3）若【回款方向】=”储蓄罐“ ：显示文案：退款将自动买入储蓄罐，金额「待退款订单退款总额+单位」金额单位：取币种文案示例：退款将自动买入储蓄罐，金额：1,000.00元若一行显示不下换行显示，显示完整为止以下资金即将到账场景，若股权产品击中，文案均统一显示 回款即将到账4、当前产品含场景【资金即将到账：分红到账】/【资金即将到账：赎回到账】/ 【资金即将到账：回款到账】时：（1）标签：回款即将到账（2）总金额=分红到账总额+赎回到账总额+回款到账总额（3）若【回款方向】=”银行卡“ 或 回款方向为空：显示文案：回款至银行卡，金额：「总金额+单位」金额单位：取币种文案示例：回款至银行卡，金额：1,000.00元若一行显示不下换行显示，显示完整为止（4）若【回款方向】=”储蓄罐“ ：显示文案：回款将自动买入储蓄罐，金额：「总金额+单位」金额单位：取币种文案示例：回款将自动买入储蓄罐，金额：1,000.00元若一行显示不下换行显示，显示完整为止标签【维持产线】清算中标签：维持产线交互说明分页：每页最多展示20条数据，若列表数据量＞20；上滑加载后20条，直至加载完全部数据排序：进入页面时默认按照初始投资成本降序排序；点击卡片，进入资产详情页，需求详见xxxx //待补充：文档链接 | 场景 | 图示 | 功能说明 | 1、无好臻持仓 |  | 1、模块标题：显示2、筛选条件区：隐藏3、内容区：（1）显示无内容图标（2）提示语：当前没有持仓资产， 去【私募股权】看看吧 ＞① “私募股权”标红处理② 点击整行文字，跳转到“私募股权”页面 | 2、有好臻持仓 |  | 1、模块标题：显示2、筛选条件区：展示，功能详见下述3、内容区：显示持仓产品卡片，功能详见下述说明4、已清仓产品：若有已清仓产品，则展示入口，功能详见下述 | 3、全部已清仓 |  | 1、模块标题：显示2、筛选条件区：隐藏3、内容区：（1）显示无内容图标（2）提示语：当前没有持仓资产， 去【私募股权】看看吧 ＞① “私募股权”标红处理② 点击整行文字，跳转到“私募股权”页面4、已清仓产品：展示入口，功能详见下述 | 字段 | 字段说明 | 图示 | （无回款）（有回款） | 基金简称【维持产线】 | 取值和显示逻辑不变 | 产品策略【新增】 | 1、取值说明：（1）产品策略：取持仓产品对应的“6+1”策略a.若为大类配置FOF，则策略对应为“多策略”是否大类配置FOF：取私募基金附加信息表<st_hedge.t_st_sm_jjxx>，限定【fof二级分类 fofejfl in (104,105,106)】剔除m_opt_type=03 的产品，取到的即为大类配置fofb.否则，取产品对应的资配一级策略，包括 股票型、股权型、CTA、另类、固收与中性根据【基金代码】jjdm，取高端标签表<st_high.t_st_gd_bq>，限制[bqdx=1]，[bqdl='301651']，[jzrq='29991231']，取【标签小类】bqxl字段，值为枚举值，枚举type=3016512、显示说明：（1）在标题下面以标签形式展示，和标题左对齐；对应产品取不到策略值，标签隐藏 | 初始投资成本【调整】 | 1、取值说明：（1）初始投资成本= 当前初始投资成本（维持产线）+ 买入待确认金额（本次新增）//注：买入待确认-首次买入场景：原初始投资成本=0，初始投资成本仅买入待确认部分（2）当前初始投资成本，维持产线，公式不变（3）买入待确认金额=当前产品的总净买入金额 - 当前产品的好基储蓄罐预支付冻结金额代销产品：若【是否计入OP】=是，则取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=4-成功 的交易记录的【净申购金额】，【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。若【申请费用】为空，则按0处理直销产品：若【是否计入OP】=否，取CRM中【录入时间】≥2022/11/1 & 【交易类型】=认购/申购 &【预约状态】=已确认 &【订单状态】=申请成功(未确认) &【打款状态】=到账确认 的预约记录的【实际打款金额】；若为外币，需按实时汇率接口转化为人民币储蓄罐预支付冻结金额：取支付方式为储蓄罐的在途订单，关联的储蓄罐预约冻结金额1、取值来源：（1）在途资产总额：取自中台接口 simu/user/balance 的 【总待确认金额-totalUnconfirmedAmt】（2）目前，中台的【总待确认金额】取份额表(cust_books)、在途表(cust_books_dtl)、crm交易确认表(CM_CUSTTRADE_DIRECT)以下字段的加总值：代销产品：份额表的【未确认金额uncomfirmed_amt】+在途表的【申请金额app_amt】直销产品：筛选出crm交易确认表的中【订单状态-ORDERSTATE】=1-申请成功，且【交易类型-busicode】=120-认购/122-申购的交易记录。取符合条件的交易记录的【申请金额-appamt】加总值；（3）补充说明：在途表和份额表是中台的表，记录代销产品的未确认金额在途表记录最新的(T-1日)的未确认金额，在T日会跑批将这份数据落到份额表；在途表和订单表关联，没有判断交易类型，但是只有认购/申购会涉及到未确认金额份额表记录除“在途表”以外的未确认金额；份额表是个总表，没有和具体的订单相关联CRM的交易确认表，记录直销产品的未确认金额，只取订单状态=1 申请成功，交易类型=认购/申购 的未确认金额（4）显示说明：同产线标题：初始投资成本(单位)；此处的单位以服务端返回的为准，可能会出现外币单位；如 投资成本(元)/ 投资成本(美元)投资成本：金额格式，四舍五入保留2位小数，如果是外币不用换算，直接显示原金额；如 1,000,000.002、解释弹窗调整说明：@待确认（1）弹窗显示说明：使用本次弹窗新样式展示指标说明（2）指标说明：共包括2块内容第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额第二块（相关说明）：分3段第1段：若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。第2段：若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。第3段：初始投资成本内未计入买入手续费。 | 产品期限说明【调整】 | 1、取值说明：当股权产品回款<=0时，显示产品期限说明字段，本次在产线的基础上优化显示逻辑调整后：（1）产品期限说明：｛投资期｝+｛退出期｝+｛延长期｝年，示例：“2+2+2年”；此处根据产品实际配置进行展示，若只维护了投资期和退出期，则只显示｛投资期｝+｛退出期｝年，示例：“2+2年”（2）字段右侧显示说明图标，点击之后弹出小气泡，小气泡显示：“投资期+退出期+延长期”，点击小气泡本身或页面任一位置，小气泡消失；小气泡内容，根据实际下发的阶段机动展示，如产品只维护了投资期和退出期，则只显示“投资期＋退出期”产线取值逻辑：：（1）前端取的中台接口的fundCXQXStr字段（2）中台取的非交易接口：com.howbuy.simu.service.base.product.SmccProductJbxxService#getSmccFromCacheByCodes 字段cxqxStr 入参是基金代码（3）非交易：取的是DB的3个字段，拼好数据返回给中台使用的，会拼接+号和单位“年”，示例：4+4+2年；* 投资期private String tzq;* 退出期private String tcq;* 延长期private String ycq;2、前端判断逻辑说明：（1）若产品的清盘中标识=是(crisisFlag=1)，则产品期限说明显示：--（2）若产品的清盘中标识=否(crisisFlag=0)，则展示产品期限说明，示例：4+4+2年2、显示说明（1）原产线的提示文案的交互优化：点击小图标后出现的气泡，点击除气泡内容外的任意位置消失（2）提示内容维持产线不变 | 已回款【维持产线】 | 取值和显示逻辑不变 | 在途提示语区域【新增】 | 1、按场景显示不同的提示语，若同时击中多种场景，均需显示（1）消息展示优先级：根据提醒事件发生顺序依次展示（2）提醒区域，支持5S轮换下一个提醒事件，循环轮播，同时支持手动滑动切换2、当前产品含场景【买入待确认（包括首次买入或追加买入）】（1）在初始投资成本下方显示提示语，直到对应订单交易确认，提醒消失（2）文案模板：含买入待确认：「买入待确认金额+币种」；示例：“含买入待确认：1,000,000.00元”，若一行显示不下换行显示// 买入待确认金额计算同初始投资成本处描述3、当前产品含场景【资金即将到账：退款到账】时：（1）标签：退款即将到账（2）若【回款方向】=”银行卡“ 或 回款方向为空：显示文案：退款至银行卡，金额「待退款订单退款总额+单位」金额单位：取币种文案示例：退款至银行卡，金额：1,000.00元若一行显示不下换行显示，显示完整为止（3）若【回款方向】=”储蓄罐“ ：显示文案：退款将自动买入储蓄罐，金额「待退款订单退款总额+单位」金额单位：取币种文案示例：退款将自动买入储蓄罐，金额：1,000.00元若一行显示不下换行显示，显示完整为止以下资金即将到账场景，若股权产品击中，文案均统一显示 回款即将到账4、当前产品含场景【资金即将到账：分红到账】/【资金即将到账：赎回到账】/ 【资金即将到账：回款到账】时：（1）标签：回款即将到账（2）总金额=分红到账总额+赎回到账总额+回款到账总额（3）若【回款方向】=”银行卡“ 或 回款方向为空：显示文案：回款至银行卡，金额：「总金额+单位」金额单位：取币种文案示例：回款至银行卡，金额：1,000.00元若一行显示不下换行显示，显示完整为止（4）若【回款方向】=”储蓄罐“ ：显示文案：回款将自动买入储蓄罐，金额：「总金额+单位」金额单位：取币种文案示例：回款将自动买入储蓄罐，金额：1,000.00元若一行显示不下换行显示，显示完整为止 | 标签【维持产线】 | 清算中标签：维持产线 | 交互说明 | 分页：每页最多展示20条数据，若列表数据量＞20；上滑加载后20条，直至加载完全部数据排序：进入页面时默认按照初始投资成本降序排序；点击卡片，进入资产详情页，需求详见xxxx //待补充：文档链接 |
| 场景 | 图示 | 功能说明 |
| 1、无好臻持仓 |  | 1、模块标题：显示2、筛选条件区：隐藏3、内容区：（1）显示无内容图标（2）提示语：当前没有持仓资产， 去【私募股权】看看吧 ＞① “私募股权”标红处理② 点击整行文字，跳转到“私募股权”页面 |
| 2、有好臻持仓 |  | 1、模块标题：显示2、筛选条件区：展示，功能详见下述3、内容区：显示持仓产品卡片，功能详见下述说明4、已清仓产品：若有已清仓产品，则展示入口，功能详见下述 |
| 3、全部已清仓 |  | 1、模块标题：显示2、筛选条件区：隐藏3、内容区：（1）显示无内容图标（2）提示语：当前没有持仓资产， 去【私募股权】看看吧 ＞① “私募股权”标红处理② 点击整行文字，跳转到“私募股权”页面4、已清仓产品：展示入口，功能详见下述 |
| 字段 | 字段说明 |
| 图示 | （无回款）（有回款） |
| 基金简称【维持产线】 | 取值和显示逻辑不变 |
| 产品策略【新增】 | 1、取值说明：（1）产品策略：取持仓产品对应的“6+1”策略a.若为大类配置FOF，则策略对应为“多策略”是否大类配置FOF：取私募基金附加信息表<st_hedge.t_st_sm_jjxx>，限定【fof二级分类 fofejfl in (104,105,106)】剔除m_opt_type=03 的产品，取到的即为大类配置fofb.否则，取产品对应的资配一级策略，包括 股票型、股权型、CTA、另类、固收与中性根据【基金代码】jjdm，取高端标签表<st_high.t_st_gd_bq>，限制[bqdx=1]，[bqdl='301651']，[jzrq='29991231']，取【标签小类】bqxl字段，值为枚举值，枚举type=3016512、显示说明：（1）在标题下面以标签形式展示，和标题左对齐；对应产品取不到策略值，标签隐藏 |
| 初始投资成本【调整】 | 1、取值说明：（1）初始投资成本= 当前初始投资成本（维持产线）+ 买入待确认金额（本次新增）//注：买入待确认-首次买入场景：原初始投资成本=0，初始投资成本仅买入待确认部分（2）当前初始投资成本，维持产线，公式不变（3）买入待确认金额=当前产品的总净买入金额 - 当前产品的好基储蓄罐预支付冻结金额代销产品：若【是否计入OP】=是，则取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=4-成功 的交易记录的【净申购金额】，【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。若【申请费用】为空，则按0处理直销产品：若【是否计入OP】=否，取CRM中【录入时间】≥2022/11/1 & 【交易类型】=认购/申购 &【预约状态】=已确认 &【订单状态】=申请成功(未确认) &【打款状态】=到账确认 的预约记录的【实际打款金额】；若为外币，需按实时汇率接口转化为人民币储蓄罐预支付冻结金额：取支付方式为储蓄罐的在途订单，关联的储蓄罐预约冻结金额1、取值来源：（1）在途资产总额：取自中台接口 simu/user/balance 的 【总待确认金额-totalUnconfirmedAmt】（2）目前，中台的【总待确认金额】取份额表(cust_books)、在途表(cust_books_dtl)、crm交易确认表(CM_CUSTTRADE_DIRECT)以下字段的加总值：代销产品：份额表的【未确认金额uncomfirmed_amt】+在途表的【申请金额app_amt】直销产品：筛选出crm交易确认表的中【订单状态-ORDERSTATE】=1-申请成功，且【交易类型-busicode】=120-认购/122-申购的交易记录。取符合条件的交易记录的【申请金额-appamt】加总值；（3）补充说明：在途表和份额表是中台的表，记录代销产品的未确认金额在途表记录最新的(T-1日)的未确认金额，在T日会跑批将这份数据落到份额表；在途表和订单表关联，没有判断交易类型，但是只有认购/申购会涉及到未确认金额份额表记录除“在途表”以外的未确认金额；份额表是个总表，没有和具体的订单相关联CRM的交易确认表，记录直销产品的未确认金额，只取订单状态=1 申请成功，交易类型=认购/申购 的未确认金额（4）显示说明：同产线标题：初始投资成本(单位)；此处的单位以服务端返回的为准，可能会出现外币单位；如 投资成本(元)/ 投资成本(美元)投资成本：金额格式，四舍五入保留2位小数，如果是外币不用换算，直接显示原金额；如 1,000,000.002、解释弹窗调整说明：@待确认（1）弹窗显示说明：使用本次弹窗新样式展示指标说明（2）指标说明：共包括2块内容第一块（指标解释）：初始投资成本 = 当前持有份额对应的初始资金成本 + 买入待确认金额第二块（相关说明）：分3段第1段：若您未发生过转受让交易，初始投资成本=私募股权实缴金额总额。第2段：若您发生过转受让交易导致份额变动时，初始投资成本按该份额的初始资金成本变动，不受实际转让/受让价格影响。第3段：初始投资成本内未计入买入手续费。 |
| 产品期限说明【调整】 | 1、取值说明：当股权产品回款<=0时，显示产品期限说明字段，本次在产线的基础上优化显示逻辑调整后：（1）产品期限说明：｛投资期｝+｛退出期｝+｛延长期｝年，示例：“2+2+2年”；此处根据产品实际配置进行展示，若只维护了投资期和退出期，则只显示｛投资期｝+｛退出期｝年，示例：“2+2年”（2）字段右侧显示说明图标，点击之后弹出小气泡，小气泡显示：“投资期+退出期+延长期”，点击小气泡本身或页面任一位置，小气泡消失；小气泡内容，根据实际下发的阶段机动展示，如产品只维护了投资期和退出期，则只显示“投资期＋退出期”产线取值逻辑：：（1）前端取的中台接口的fundCXQXStr字段（2）中台取的非交易接口：com.howbuy.simu.service.base.product.SmccProductJbxxService#getSmccFromCacheByCodes 字段cxqxStr 入参是基金代码（3）非交易：取的是DB的3个字段，拼好数据返回给中台使用的，会拼接+号和单位“年”，示例：4+4+2年；* 投资期private String tzq;* 退出期private String tcq;* 延长期private String ycq;2、前端判断逻辑说明：（1）若产品的清盘中标识=是(crisisFlag=1)，则产品期限说明显示：--（2）若产品的清盘中标识=否(crisisFlag=0)，则展示产品期限说明，示例：4+4+2年2、显示说明（1）原产线的提示文案的交互优化：点击小图标后出现的气泡，点击除气泡内容外的任意位置消失（2）提示内容维持产线不变 |
| 已回款【维持产线】 | 取值和显示逻辑不变 |
| 在途提示语区域【新增】 | 1、按场景显示不同的提示语，若同时击中多种场景，均需显示（1）消息展示优先级：根据提醒事件发生顺序依次展示（2）提醒区域，支持5S轮换下一个提醒事件，循环轮播，同时支持手动滑动切换2、当前产品含场景【买入待确认（包括首次买入或追加买入）】（1）在初始投资成本下方显示提示语，直到对应订单交易确认，提醒消失（2）文案模板：含买入待确认：「买入待确认金额+币种」；示例：“含买入待确认：1,000,000.00元”，若一行显示不下换行显示// 买入待确认金额计算同初始投资成本处描述3、当前产品含场景【资金即将到账：退款到账】时：（1）标签：退款即将到账（2）若【回款方向】=”银行卡“ 或 回款方向为空：显示文案：退款至银行卡，金额「待退款订单退款总额+单位」金额单位：取币种文案示例：退款至银行卡，金额：1,000.00元若一行显示不下换行显示，显示完整为止（3）若【回款方向】=”储蓄罐“ ：显示文案：退款将自动买入储蓄罐，金额「待退款订单退款总额+单位」金额单位：取币种文案示例：退款将自动买入储蓄罐，金额：1,000.00元若一行显示不下换行显示，显示完整为止以下资金即将到账场景，若股权产品击中，文案均统一显示 回款即将到账4、当前产品含场景【资金即将到账：分红到账】/【资金即将到账：赎回到账】/ 【资金即将到账：回款到账】时：（1）标签：回款即将到账（2）总金额=分红到账总额+赎回到账总额+回款到账总额（3）若【回款方向】=”银行卡“ 或 回款方向为空：显示文案：回款至银行卡，金额：「总金额+单位」金额单位：取币种文案示例：回款至银行卡，金额：1,000.00元若一行显示不下换行显示，显示完整为止（4）若【回款方向】=”储蓄罐“ ：显示文案：回款将自动买入储蓄罐，金额：「总金额+单位」金额单位：取币种文案示例：回款将自动买入储蓄罐，金额：1,000.00元若一行显示不下换行显示，显示完整为止 |
| 标签【维持产线】 | 清算中标签：维持产线 |
| 交互说明 | 分页：每页最多展示20条数据，若列表数据量＞20；上滑加载后20条，直至加载完全部数据排序：进入页面时默认按照初始投资成本降序排序；点击卡片，进入资产详情页，需求详见xxxx //待补充：文档链接 |
|  | 8、已清仓产品入口 | 1、取值逻辑（1）清仓产品列表：根据客户一账通，调高端中台接口获取客户好臻分销下的清仓产品，需要返回产品代码、基金简称、累计持有天数等 @彭程 需要新增接口数据按产品代码维度返回，若同一个产品被清仓多次，则该产品返回1条清仓记录（2）清仓产品个数：汇总接口返回的清仓产品个数2、交互说明（1）显隐说明：若清仓产品个数 > 0，则展示此入口；若清仓产品个数 = 0，则隐藏此入口（2）入口样式参考UI，展示文案：已清仓产品(${清仓产品个数})（3）点击按钮，跳转至<已清仓产品页>，详情参考下述 5.1 清仓产品列表 的说明 |
|  | 9、底部区域 | 1、整体说明：底部区域包含三部分，即：主体说明、免责声明、帮助中心入口 （按此顺序展示）2、页面展示：（1）主体说明，固定文案：—— 基金销售服务由好臻投资有限公司提供 ——（2）免责声明：若客户持有好臻产品，则展示免责声明，固定文案：免责申明：尊敬的投资者，通过您在本公司的历史投资记录，您已被认定为专业投资者。根据《证券期货投资者适当性管理办法》，本公司应对不同类别投资者履行适当性义务的差别，警示可能承担的投资风险。普通投资者在信息告知、 风险警示、适当性匹配等方面将享有特别保护。符合条件的专业投资者，也可以书面告知本公司选择成为普通投资者。以上请您知晓。如有疑问，请联系您的投资顾问或拨打好买客服热线4007009665。若客户不持有好臻产品，则不展示免责声明；（3）帮助中心：固定底部展示按钮，点击进入<帮助中心页面> //待贴入帮助中心文档地址 |